use sqlx::{Pool, Sqlite, SqlitePool};
use std::path::Path;
use tracing::{info, warn};

use shared::{AppError, AppResult};

pub mod models;
pub mod repository;

pub use models::*;
pub use repository::*;

/// 数据库连接池管理器
#[derive(Clone)]
pub struct DatabaseManager {
    pool: Pool<Sqlite>,
}

impl DatabaseManager {
    /// 创建新的数据库管理器
    pub async fn new(database_url: &str) -> AppResult<Self> {
        info!("Connecting to database: {}", database_url);

        // 创建连接池
        let pool = SqlitePool::connect(database_url)
            .await
            .map_err(|e| {
                AppError::Database(e)
            })?;

        let manager = Self { pool };

        // 运行数据库迁移
        manager.run_migrations().await?;

        info!("Database connection established successfully");
        Ok(manager)
    }

    /// 获取数据库连接池
    pub fn pool(&self) -> &Pool<Sqlite> {
        &self.pool
    }

    /// 运行数据库迁移
    async fn run_migrations(&self) -> AppResult<()> {
        info!("Running database migrations...");

        // 读取迁移文件
        let migration_sql = include_str!("../../migrations/001_initial.sql");

        // 执行迁移
        sqlx::query(migration_sql)
            .execute(&self.pool)
            .await
            .map_err(AppError::Database)?;

        info!("Database migrations completed successfully");
        Ok(())
    }

    /// 健康检查
    pub async fn health_check(&self) -> AppResult<()> {
        sqlx::query("SELECT 1")
            .execute(&self.pool)
            .await
            .map_err(AppError::Database)?;

        Ok(())
    }

    /// 创建用户仓库
    pub fn user_repository(&self) -> repository::SqliteUserRepository {
        repository::SqliteUserRepository::new(self.pool.clone())
    }

    /// 创建笔记仓库
    pub fn note_repository(&self) -> repository::SqliteNoteRepository {
        repository::SqliteNoteRepository::new(self.pool.clone())
    }

    /// 创建模板仓库
    pub fn template_repository(&self) -> repository::SqliteTemplateRepository {
        repository::SqliteTemplateRepository::new(self.pool.clone())
    }
}

/// 数据库配置
#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: "sqlite:./securefox.db".to_string(),
            max_connections: 10,
            min_connections: 1,
        }
    }
}
