{"rustc": 5148484765029645790, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17878142613068009629, "path": 9419930566792415913, "deps": [[5103565458935487, "futures_io", false, 11389401264103957335], [1615478164327904835, "pin_utils", false, 9277268140708750126], [1906322745568073236, "pin_project_lite", false, 15883797645417401044], [5451793922601807560, "slab", false, 1219510504332707815], [7013762810557009322, "futures_sink", false, 288007236704285166], [7620660491849607393, "futures_core", false, 16247032716957650489], [15932120279885307830, "memchr", false, 7063427389844874179], [16240732885093539806, "futures_task", false, 7992079856657690461]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-f75b768bd68b18c5/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}