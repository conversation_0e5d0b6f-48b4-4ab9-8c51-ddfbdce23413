// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#a194fea0-d065-4b72-85be-8e349bb0f85e"
//   Timestamp: "2025-08-05T16:40:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "笔记状态管理完整，支持CRUD操作和缓存"
// }}
// 笔记状态管理

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { 
  SecureNote, 
  CreateNoteRequest, 
  UpdateNoteRequest, 
  NoteType,
  TotpResponse 
} from '../types';
import { apiClient } from '../utils/api';
import { CacheStorage } from '../utils/storage';
import { showErrorNotification, showSuccessNotification } from '../utils/messaging';

export const useNotesStore = defineStore('notes', () => {
  // 状态
  const notes = ref<SecureNote[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const selectedType = ref<NoteType | 'all'>('all');
  const showFavoritesOnly = ref(false);

  // 计算属性
  const filteredNotes = computed(() => {
    let filtered = notes.value;

    // 按类型过滤
    if (selectedType.value !== 'all') {
      filtered = filtered.filter(note => note.note_type === selectedType.value);
    }

    // 按收藏过滤
    if (showFavoritesOnly.value) {
      filtered = filtered.filter(note => note.is_favorite);
    }

    // 按搜索查询过滤
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(note => 
        note.title.toLowerCase().includes(query) ||
        note.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return filtered;
  });

  const notesByType = computed(() => {
    const grouped: Record<string, SecureNote[]> = {};
    
    notes.value.forEach(note => {
      const type = note.note_type;
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(note);
    });

    return grouped;
  });

  const favoriteNotes = computed(() => 
    notes.value.filter(note => note.is_favorite)
  );

  const recentNotes = computed(() => 
    [...notes.value]
      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
      .slice(0, 10)
  );

  // 初始化笔记数据
  async function initialize(): Promise<void> {
    try {
      isLoading.value = true;
      error.value = null;

      // 先从缓存加载
      const cachedNotes = await CacheStorage.getNotes();
      if (cachedNotes.length > 0) {
        notes.value = cachedNotes;
      }

      // 然后从API获取最新数据
      await fetchNotes();
    } catch (err) {
      console.error('Failed to initialize notes:', err);
      error.value = 'Failed to load notes';
    } finally {
      isLoading.value = false;
    }
  }

  // 获取所有笔记
  async function fetchNotes(): Promise<void> {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await apiClient.getNotes();

      if (response.success && response.data) {
        notes.value = response.data;
        
        // 更新缓存
        await CacheStorage.setNotes(response.data);
      } else {
        error.value = response.error || 'Failed to fetch notes';
        await showErrorNotification(error.value);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notes';
      error.value = errorMessage;
      await showErrorNotification(errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  // 获取单个笔记
  async function fetchNote(id: string): Promise<SecureNote | null> {
    try {
      const response = await apiClient.getNote(id);

      if (response.success && response.data) {
        // 更新本地缓存中的笔记
        const index = notes.value.findIndex(n => n.id === id);
        if (index !== -1) {
          notes.value[index] = response.data;
        } else {
          notes.value.push(response.data);
        }

        await CacheStorage.setNotes(notes.value);
        return response.data;
      } else {
        error.value = response.error || 'Failed to fetch note';
        await showErrorNotification(error.value);
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch note';
      error.value = errorMessage;
      await showErrorNotification(errorMessage);
      return null;
    }
  }

  // 创建笔记
  async function createNote(noteData: CreateNoteRequest): Promise<SecureNote | null> {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await apiClient.createNote(noteData);

      if (response.success && response.data) {
        notes.value.push(response.data);
        
        // 更新缓存
        await CacheStorage.addNote(response.data);
        
        await showSuccessNotification('笔记创建成功');
        return response.data;
      } else {
        error.value = response.error || 'Failed to create note';
        await showErrorNotification(error.value);
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create note';
      error.value = errorMessage;
      await showErrorNotification(errorMessage);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // 更新笔记
  async function updateNote(id: string, updates: UpdateNoteRequest): Promise<SecureNote | null> {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await apiClient.updateNote(id, updates);

      if (response.success && response.data) {
        const index = notes.value.findIndex(n => n.id === id);
        if (index !== -1) {
          notes.value[index] = response.data;
        }

        // 更新缓存
        await CacheStorage.updateNote(id, response.data);
        
        await showSuccessNotification('笔记更新成功');
        return response.data;
      } else {
        error.value = response.error || 'Failed to update note';
        await showErrorNotification(error.value);
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update note';
      error.value = errorMessage;
      await showErrorNotification(errorMessage);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // 删除笔记
  async function deleteNote(id: string): Promise<boolean> {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await apiClient.deleteNote(id);

      if (response.success) {
        notes.value = notes.value.filter(n => n.id !== id);
        
        // 更新缓存
        await CacheStorage.removeNote(id);
        
        await showSuccessNotification('笔记删除成功');
        return true;
      } else {
        error.value = response.error || 'Failed to delete note';
        await showErrorNotification(error.value);
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete note';
      error.value = errorMessage;
      await showErrorNotification(errorMessage);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // 搜索笔记
  async function searchNotes(query: string): Promise<void> {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await apiClient.searchNotes(query);

      if (response.success && response.data) {
        // 这里可以选择替换当前笔记列表或者标记搜索结果
        // 暂时更新搜索查询，让计算属性处理过滤
        searchQuery.value = query;
      } else {
        error.value = response.error || 'Search failed';
        await showErrorNotification(error.value);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed';
      error.value = errorMessage;
      await showErrorNotification(errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  // 按类型获取笔记
  async function fetchNotesByType(type: NoteType): Promise<void> {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await apiClient.getNotesByType(type);

      if (response.success && response.data) {
        // 更新选中的类型
        selectedType.value = type;
        
        // 可以选择只显示这些笔记或者合并到现有列表
        // 这里选择设置过滤器
      } else {
        error.value = response.error || 'Failed to fetch notes by type';
        await showErrorNotification(error.value);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notes by type';
      error.value = errorMessage;
      await showErrorNotification(errorMessage);
    } finally {
      isLoading.value = false;
    }
  }

  // 生成TOTP验证码
  async function generateTotp(noteId: string): Promise<TotpResponse | null> {
    try {
      const response = await apiClient.generateTotp(noteId);

      if (response.success && response.data) {
        return response.data;
      } else {
        error.value = response.error || 'Failed to generate TOTP';
        await showErrorNotification(error.value);
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate TOTP';
      error.value = errorMessage;
      await showErrorNotification(errorMessage);
      return null;
    }
  }

  // 切换收藏状态
  async function toggleFavorite(id: string): Promise<void> {
    const note = notes.value.find(n => n.id === id);
    if (!note) return;

    await updateNote(id, { is_favorite: !note.is_favorite });
  }

  // 清除搜索
  function clearSearch(): void {
    searchQuery.value = '';
  }

  // 设置类型过滤器
  function setTypeFilter(type: NoteType | 'all'): void {
    selectedType.value = type;
  }

  // 切换收藏过滤器
  function toggleFavoritesFilter(): void {
    showFavoritesOnly.value = !showFavoritesOnly.value;
  }

  // 清除错误
  function clearError(): void {
    error.value = null;
  }

  // 获取笔记统计
  const getStats = computed(() => ({
    total: notes.value.length,
    favorites: favoriteNotes.value.length,
    byType: Object.entries(notesByType.value).map(([type, typeNotes]) => ({
      type,
      count: typeNotes.length,
    })),
  }));

  return {
    // 状态
    notes,
    isLoading,
    error,
    searchQuery,
    selectedType,
    showFavoritesOnly,
    
    // 计算属性
    filteredNotes,
    notesByType,
    favoriteNotes,
    recentNotes,
    getStats,
    
    // 方法
    initialize,
    fetchNotes,
    fetchNote,
    createNote,
    updateNote,
    deleteNote,
    searchNotes,
    fetchNotesByType,
    generateTotp,
    toggleFavorite,
    clearSearch,
    setTypeFilter,
    toggleFavoritesFilter,
    clearError,
  };
});
