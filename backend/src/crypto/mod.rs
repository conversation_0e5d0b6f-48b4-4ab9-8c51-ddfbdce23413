// {{RIPER-5: Action:"Added", Task_ID:"#8636e378", Timestamp:"2025-08-05", Role:"LD", Principle:"SOLID-S"}}
// 加密服务模块 - 零知识加密架构核心

pub mod encryption;
pub mod key_derivation;
pub mod secure_memory;

#[cfg(test)]
mod performance_tests;

pub use encryption::*;
pub use key_derivation::*;
pub use secure_memory::*;

use anyhow::Result;
use async_trait::async_trait;

/// 加密服务trait - 定义核心加密操作接口
#[async_trait]
pub trait CryptoService: Send + Sync {
    /// 从主密码派生主密钥
    async fn derive_master_key(&self, master_password: &str, salt: &[u8]) -> Result<MasterKey>;

    /// 验证主密码
    async fn verify_master_password(&self, master_password: &str, salt: &[u8], stored_key: &[u8]) -> Result<bool>;

    /// 加密数据
    async fn encrypt_data(&self, data: &str, key: &[u8]) -> Result<EncryptedData>;

    /// 解密数据
    async fn decrypt_data(&self, encrypted_data: &EncryptedData, key: &[u8]) -> Result<String>;

    /// 生成安全随机数
    async fn generate_random_bytes(&self, length: usize) -> Result<Vec<u8>>;

    /// 生成盐值
    async fn generate_salt(&self) -> Result<[u8; 16]>;
}

/// 默认加密服务实现
pub struct DefaultCryptoService {
    encryption_service: EncryptionService,
}

impl Default for DefaultCryptoService {
    fn default() -> Self {
        Self::new()
    }
}

impl DefaultCryptoService {
    /// 创建新的加密服务实例
    pub fn new() -> Self {
        Self {
            encryption_service: EncryptionService::new(),
        }
    }
}

#[async_trait]
impl CryptoService for DefaultCryptoService {
    async fn derive_master_key(&self, master_password: &str, salt: &[u8]) -> Result<MasterKey> {
        // 在异步上下文中执行CPU密集型操作
        let master_password = master_password.to_string();
        let salt = salt.to_vec();

        tokio::task::spawn_blocking(move || {
            KeyDerivationService::derive_master_key(&master_password, &salt)
        }).await?
    }

    async fn verify_master_password(&self, master_password: &str, salt: &[u8], stored_key: &[u8]) -> Result<bool> {
        let master_password = master_password.to_string();
        let salt = salt.to_vec();
        let stored_key = stored_key.to_vec();

        tokio::task::spawn_blocking(move || {
            KeyDerivationService::verify_master_password(&master_password, &salt, &stored_key)
        }).await?
    }

    async fn encrypt_data(&self, data: &str, key: &[u8]) -> Result<EncryptedData> {
        self.encryption_service.encrypt_data(data, key)
    }

    async fn decrypt_data(&self, encrypted_data: &EncryptedData, key: &[u8]) -> Result<String> {
        self.encryption_service.decrypt_data(encrypted_data, key)
    }

    async fn generate_random_bytes(&self, length: usize) -> Result<Vec<u8>> {
        self.encryption_service.generate_random_bytes(length)
    }

    async fn generate_salt(&self) -> Result<[u8; 16]> {
        self.encryption_service.generate_salt()
    }
}
