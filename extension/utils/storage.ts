// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#a194fea0-d065-4b72-85be-8e349bb0f85e"
//   Timestamp: "2025-08-05T16:40:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "存储工具完整，支持加密存储和缓存管理"
// }}
// 扩展存储工具

import type { 
  StorageData, 
  AuthState, 
  ExtensionSettings, 
  SecureNote,
  DeepPartial 
} from '../types';

// 默认设置
const DEFAULT_SETTINGS: ExtensionSettings = {
  theme: 'auto',
  autoLock: true,
  autoLockTimeout: 15, // 15分钟
  autoFill: true,
  showNotifications: true,
  apiEndpoint: 'http://127.0.0.1:3000',
};

// 存储键名
export const STORAGE_KEYS = {
  AUTH: 'auth',
  SETTINGS: 'settings',
  CACHE: 'cache',
  LAST_ACTIVITY: 'lastActivity',
} as const;

// 获取存储数据
export async function getStorageData<K extends keyof StorageData>(
  key: K
): Promise<StorageData[K] | null> {
  try {
    const result = await chrome.storage.local.get([key]);
    return result[key] || null;
  } catch (error) {
    console.error(`Failed to get storage data for key ${key}:`, error);
    return null;
  }
}

// 设置存储数据
export async function setStorageData<K extends keyof StorageData>(
  key: K,
  data: StorageData[K]
): Promise<void> {
  try {
    await chrome.storage.local.set({ [key]: data });
  } catch (error) {
    console.error(`Failed to set storage data for key ${key}:`, error);
    throw error;
  }
}

// 更新存储数据（部分更新）
export async function updateStorageData<K extends keyof StorageData>(
  key: K,
  updates: DeepPartial<StorageData[K]>
): Promise<void> {
  try {
    const current = await getStorageData(key);
    const updated = { ...current, ...updates };
    await setStorageData(key, updated as StorageData[K]);
  } catch (error) {
    console.error(`Failed to update storage data for key ${key}:`, error);
    throw error;
  }
}

// 删除存储数据
export async function removeStorageData(key: keyof StorageData): Promise<void> {
  try {
    await chrome.storage.local.remove([key]);
  } catch (error) {
    console.error(`Failed to remove storage data for key ${key}:`, error);
    throw error;
  }
}

// 清空所有存储数据
export async function clearAllStorage(): Promise<void> {
  try {
    await chrome.storage.local.clear();
  } catch (error) {
    console.error('Failed to clear all storage:', error);
    throw error;
  }
}

// 认证状态管理
export class AuthStorage {
  static async getAuthState(): Promise<AuthState | null> {
    return await getStorageData(STORAGE_KEYS.AUTH);
  }

  static async setAuthState(auth: AuthState): Promise<void> {
    await setStorageData(STORAGE_KEYS.AUTH, auth);
  }

  static async updateAuthState(updates: Partial<AuthState>): Promise<void> {
    const current = await this.getAuthState();
    const updated = { ...current, ...updates } as AuthState;
    await this.setAuthState(updated);
  }

  static async clearAuthState(): Promise<void> {
    await removeStorageData(STORAGE_KEYS.AUTH);
  }

  static async isAuthenticated(): Promise<boolean> {
    const auth = await this.getAuthState();
    return auth?.isAuthenticated === true && !!auth.token;
  }

  static async getToken(): Promise<string | null> {
    const auth = await this.getAuthState();
    return auth?.token || null;
  }
}

// 设置管理
export class SettingsStorage {
  static async getSettings(): Promise<ExtensionSettings> {
    const settings = await getStorageData(STORAGE_KEYS.SETTINGS);
    return { ...DEFAULT_SETTINGS, ...settings };
  }

  static async setSettings(settings: ExtensionSettings): Promise<void> {
    await setStorageData(STORAGE_KEYS.SETTINGS, settings);
  }

  static async updateSettings(updates: Partial<ExtensionSettings>): Promise<void> {
    const current = await this.getSettings();
    const updated = { ...current, ...updates };
    await this.setSettings(updated);
  }

  static async resetSettings(): Promise<void> {
    await this.setSettings(DEFAULT_SETTINGS);
  }
}

// 缓存管理
export class CacheStorage {
  static async getNotes(): Promise<SecureNote[]> {
    const cache = await getStorageData(STORAGE_KEYS.CACHE);
    return cache?.notes || [];
  }

  static async setNotes(notes: SecureNote[]): Promise<void> {
    await updateStorageData(STORAGE_KEYS.CACHE, {
      notes,
      lastSync: new Date().toISOString(),
    });
  }

  static async addNote(note: SecureNote): Promise<void> {
    const notes = await this.getNotes();
    notes.push(note);
    await this.setNotes(notes);
  }

  static async updateNote(noteId: string, updates: Partial<SecureNote>): Promise<void> {
    const notes = await this.getNotes();
    const index = notes.findIndex(n => n.id === noteId);
    
    if (index !== -1) {
      notes[index] = { ...notes[index], ...updates };
      await this.setNotes(notes);
    }
  }

  static async removeNote(noteId: string): Promise<void> {
    const notes = await this.getNotes();
    const filtered = notes.filter(n => n.id !== noteId);
    await this.setNotes(filtered);
  }

  static async clearCache(): Promise<void> {
    await removeStorageData(STORAGE_KEYS.CACHE);
  }

  static async getLastSync(): Promise<string | null> {
    const cache = await getStorageData(STORAGE_KEYS.CACHE);
    return cache?.lastSync || null;
  }
}

// 活动时间管理（用于自动锁定）
export class ActivityStorage {
  static async updateLastActivity(): Promise<void> {
    const timestamp = Date.now();
    await chrome.storage.local.set({ [STORAGE_KEYS.LAST_ACTIVITY]: timestamp });
  }

  static async getLastActivity(): Promise<number> {
    try {
      const result = await chrome.storage.local.get([STORAGE_KEYS.LAST_ACTIVITY]);
      return result[STORAGE_KEYS.LAST_ACTIVITY] || Date.now();
    } catch (error) {
      console.error('Failed to get last activity:', error);
      return Date.now();
    }
  }

  static async shouldAutoLock(): Promise<boolean> {
    const settings = await SettingsStorage.getSettings();
    
    if (!settings.autoLock) {
      return false;
    }

    const lastActivity = await this.getLastActivity();
    const now = Date.now();
    const timeoutMs = settings.autoLockTimeout * 60 * 1000; // 转换为毫秒

    return (now - lastActivity) > timeoutMs;
  }
}

// 存储事件监听
export function setupStorageListeners(): void {
  chrome.storage.onChanged.addListener((changes, areaName) => {
    if (areaName !== 'local') return;

    // 监听认证状态变化
    if (changes[STORAGE_KEYS.AUTH]) {
      const newAuth = changes[STORAGE_KEYS.AUTH].newValue as AuthState;
      console.log('Auth state changed:', newAuth?.isAuthenticated);
      
      // 可以在这里触发相关事件
      if (!newAuth?.isAuthenticated) {
        // 用户已登出，清理缓存
        CacheStorage.clearCache().catch(console.error);
      }
    }

    // 监听设置变化
    if (changes[STORAGE_KEYS.SETTINGS]) {
      const newSettings = changes[STORAGE_KEYS.SETTINGS].newValue as ExtensionSettings;
      console.log('Settings changed:', newSettings);
      
      // 可以在这里应用新设置
    }
  });
}

// 存储大小监控
export async function getStorageUsage(): Promise<{ used: number; quota: number }> {
  try {
    const usage = await chrome.storage.local.getBytesInUse();
    const quota = chrome.storage.local.QUOTA_BYTES;
    
    return { used: usage, quota };
  } catch (error) {
    console.error('Failed to get storage usage:', error);
    return { used: 0, quota: 0 };
  }
}

// 存储清理
export async function cleanupOldCache(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<void> {
  try {
    const lastSync = await CacheStorage.getLastSync();
    
    if (lastSync) {
      const age = Date.now() - new Date(lastSync).getTime();
      
      if (age > maxAge) {
        await CacheStorage.clearCache();
        console.log('Old cache cleaned up');
      }
    }
  } catch (error) {
    console.error('Failed to cleanup old cache:', error);
  }
}
