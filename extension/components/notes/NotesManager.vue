<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "笔记管理器组件完整，支持列表和编辑模式切换"
}} -->
<template>
  <div class="notes-manager">
    <!-- 列表视图 -->
    <div v-if="currentView === 'list'" class="list-view">
      <NoteList
        @create-note="handleCreateNote"
        @note-click="handleNoteClick"
        @edit-note="handleEditNote"
        @delete-note="handleDeleteNote"
      />
    </div>

    <!-- 编辑视图 -->
    <div v-else-if="currentView === 'edit'" class="edit-view">
      <NoteEditor
        :note="currentNote"
        :loading="notesStore.isLoading"
        @save="handleSaveNote"
        @cancel="handleCancelEdit"
      />
    </div>

    <!-- 详情视图 -->
    <div v-else-if="currentView === 'detail'" class="detail-view">
      <NoteDetail
        :note="currentNote!"
        @edit="handleEditNote"
        @delete="handleDeleteNote"
        @close="handleCloseDetail"
      />
    </div>

    <!-- 删除确认对话框 -->
    <n-modal
      v-model:show="showDeleteDialog"
      preset="dialog"
      type="warning"
      title="删除笔记"
      :content="`确定要删除笔记 「${noteToDelete?.title}」 吗？此操作不可撤销。`"
      positive-text="删除"
      negative-text="取消"
      @positive-click="confirmDelete"
      @negative-click="showDeleteDialog = false"
    />

    <!-- 错误提示 -->
    <n-alert
      v-if="notesStore.error"
      type="error"
      :title="notesStore.error"
      closable
      @close="notesStore.clearError"
      style="margin: 16px;"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NModal, NAlert, useMessage } from 'naive-ui';
import { useNotesStore } from '../../stores/notes';
import NoteList from './NoteList.vue';
import NoteEditor from './NoteEditor.vue';
import NoteDetail from './NoteDetail.vue';
import type { SecureNote, CreateNoteRequest, UpdateNoteRequest } from '../../types';

// 状态管理
const notesStore = useNotesStore();
const message = useMessage();

// 响应式状态
const currentView = ref<'list' | 'edit' | 'detail'>('list');
const currentNote = ref<SecureNote | undefined>();
const showDeleteDialog = ref(false);
const noteToDelete = ref<SecureNote | null>(null);

// 方法
const handleCreateNote = () => {
  currentNote.value = undefined;
  currentView.value = 'edit';
};

const handleNoteClick = (note: SecureNote) => {
  currentNote.value = note;
  currentView.value = 'detail';
};

const handleEditNote = (note: SecureNote) => {
  currentNote.value = note;
  currentView.value = 'edit';
};

const handleDeleteNote = (note: SecureNote) => {
  noteToDelete.value = note;
  showDeleteDialog.value = true;
};

const handleSaveNote = async (data: CreateNoteRequest | UpdateNoteRequest) => {
  try {
    let savedNote: SecureNote | null = null;

    if (currentNote.value) {
      // 更新笔记
      savedNote = await notesStore.updateNote(currentNote.value.id, data as UpdateNoteRequest);
    } else {
      // 创建笔记
      savedNote = await notesStore.createNote(data as CreateNoteRequest);
    }

    if (savedNote) {
      message.success(currentNote.value ? '笔记更新成功' : '笔记创建成功');
      currentNote.value = savedNote;
      currentView.value = 'detail';
    }
  } catch (error) {
    console.error('Save note failed:', error);
    message.error('保存失败');
  }
};

const handleCancelEdit = () => {
  if (currentNote.value) {
    currentView.value = 'detail';
  } else {
    currentView.value = 'list';
  }
};

const handleCloseDetail = () => {
  currentView.value = 'list';
  currentNote.value = undefined;
};

const confirmDelete = async () => {
  if (!noteToDelete.value) return;

  try {
    const success = await notesStore.deleteNote(noteToDelete.value.id);
    
    if (success) {
      message.success('笔记删除成功');
      
      // 如果删除的是当前显示的笔记，返回列表
      if (currentNote.value?.id === noteToDelete.value.id) {
        currentView.value = 'list';
        currentNote.value = undefined;
      }
    }
  } catch (error) {
    console.error('Delete note failed:', error);
    message.error('删除失败');
  } finally {
    showDeleteDialog.value = false;
    noteToDelete.value = null;
  }
};

// 生命周期
onMounted(async () => {
  // 初始化笔记数据
  if (notesStore.notes.length === 0) {
    await notesStore.initialize();
  }
});
</script>

<style scoped>
.notes-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.list-view,
.edit-view,
.detail-view {
  flex: 1;
  overflow: hidden;
}

.edit-view {
  padding: 16px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .edit-view {
    padding: 12px;
  }
}
</style>
