// {{RIPER-5:
//   Action: "Modified"
//   Task_ID: "#8db4a073-e2f1-4c70-9e28-e31d3e70f268"
//   Timestamp: "2025-08-05T15:30:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "API路由配置完整，包含认证路由和中间件"
// }}
// API路由模块

use axum::{
    routing::{get, post, put, delete},
    Router,
    middleware,
};

use crate::auth::{
    handlers::{register_handler, login_handler, verify_handler, logout_handler},
    middleware::jwt_auth_middleware,
};
use crate::notes::handlers::{
    get_notes_handler, create_note_handler, get_note_handler, update_note_handler,
    delete_note_handler, get_notes_by_type_handler, search_notes_handler, generate_totp_handler,
};
use crate::database::DatabaseManager;

/// 创建API路由
pub fn create_routes() -> Router<DatabaseManager> {
    // 无需认证的路由
    let public_routes = Router::new()
        .route("/api/auth/register", post(register_handler))
        .route("/api/auth/login", post(login_handler));

    // 需要认证的路由
    let protected_routes = Router::new()
        // 认证相关
        .route("/api/auth/verify", get(verify_handler))
        .route("/api/auth/logout", post(logout_handler))
        // 笔记管理
        .route("/api/notes", get(get_notes_handler))
        .route("/api/notes", post(create_note_handler))
        .route("/api/notes/:id", get(get_note_handler))
        .route("/api/notes/:id", put(update_note_handler))
        .route("/api/notes/:id", delete(delete_note_handler))
        .route("/api/notes/types/:type", get(get_notes_by_type_handler))
        .route("/api/notes/search", get(search_notes_handler))
        .route("/api/totp/:id/generate", get(generate_totp_handler))
        .layer(middleware::from_fn(jwt_auth_middleware));

    // 合并路由
    public_routes.merge(protected_routes)
        // 其他API路由将在后续任务中添加
}
