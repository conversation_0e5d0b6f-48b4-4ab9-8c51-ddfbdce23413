use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// 数据库用户模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct DbUser {
    pub id: String,
    pub email: String,
    pub password_hash: String,
    pub salt: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 数据库安全笔记模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct DbSecureNote {
    pub id: String,
    pub user_id: String,
    pub title: String,
    pub note_type: String,
    pub content: String, // JSON字符串
    pub tags: String,    // JSON数组字符串
    pub is_favorite: bool,
    pub template_id: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 数据库笔记模板模型
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct DbNoteTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub content_template: String, // JSON字符串
    pub category: String,
    pub is_system: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 用户创建请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateUserRequest {
    pub email: String,
    pub password_hash: String,
    pub salt: String,
}

/// 笔记创建请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateNoteRequest {
    pub user_id: String,
    pub title: String,
    pub note_type: String,
    pub content: String,
    pub tags: String,
    pub is_favorite: Option<bool>,
    pub template_id: Option<String>,
}

/// 笔记更新请求
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateNoteRequest {
    pub title: Option<String>,
    pub content: Option<String>,
    pub tags: Option<String>,
    pub is_favorite: Option<bool>,
}

impl DbUser {
    /// 转换为共享类型的User
    pub fn to_user(&self) -> shared::User {
        shared::User {
            id: Uuid::parse_str(&self.id).unwrap_or_default(),
            email: self.email.clone(),
            created_at: self.created_at,
        }
    }
}

impl DbSecureNote {
    /// 转换为共享类型的SecureNote
    pub fn to_secure_note(&self) -> Result<shared::SecureNote, serde_json::Error> {
        let note_type = match self.note_type.as_str() {
            "login" => shared::NoteType::Login,
            "ssh_key" => shared::NoteType::SshKey,
            "api_key" => shared::NoteType::ApiKey,
            "certificate" => shared::NoteType::Certificate,
            "database" => shared::NoteType::Database,
            "totp" => shared::NoteType::Totp,
            "tech_note" => shared::NoteType::TechNote,
            _ => shared::NoteType::TechNote, // 默认值
        };

        let content: serde_json::Value = serde_json::from_str(&self.content)?;
        let tags: Vec<String> = serde_json::from_str(&self.tags).unwrap_or_default();

        Ok(shared::SecureNote {
            id: Uuid::parse_str(&self.id).unwrap_or_default(),
            user_id: Uuid::parse_str(&self.user_id).unwrap_or_default(),
            title: self.title.clone(),
            note_type,
            content,
            tags,
            is_favorite: self.is_favorite,
            template_id: self.template_id.as_ref().and_then(|id| Uuid::parse_str(id).ok()),
            created_at: self.created_at,
            updated_at: self.updated_at,
        })
    }
}
