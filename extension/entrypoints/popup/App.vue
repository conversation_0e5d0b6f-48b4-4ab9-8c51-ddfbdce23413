<template>
  <div class="app">
    <n-config-provider :theme="theme">
      <n-message-provider>
        <n-loading-bar-provider>
          <div class="container">
            <!-- 头部 -->
            <header class="header">
              <h1 class="title">
                <n-icon size="24" class="icon">
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
                  </svg>
                </n-icon>
                SecureFox
              </h1>
              <n-space>
                <!-- 用户信息 -->
                <span v-if="authStore.isLoggedIn" class="user-info">
                  {{ authStore.userEmail }}
                </span>
                <!-- 主题切换 -->
                <n-button
                  quaternary
                  circle
                  size="small"
                  @click="toggleTheme"
                >
                  <n-icon size="16">
                    <svg v-if="isDark" viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12,18C11.11,18 10.26,17.8 9.5,17.45C11.56,16.5 13,14.42 13,12C13,9.58 11.56,7.5 9.5,6.55C10.26,6.2 11.11,6 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z" />
                    </svg>
                    <svg v-else viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z" />
                    </svg>
                  </n-icon>
                </n-button>
              </n-space>
            </header>

            <!-- 主内容区域 -->
            <main class="main">
              <!-- 加载状态 -->
              <div v-if="isInitializing" class="loading-container">
                <n-spin size="large">
                  <template #description>
                    正在初始化...
                  </template>
                </n-spin>
              </div>

              <!-- 未认证状态 - 显示登录/注册界面 -->
              <AuthContainer v-else-if="!authStore.isLoggedIn" />

              <!-- 已认证状态 - 显示主界面 -->
              <div v-else class="main-content">
                <!-- 会话状态 -->
                <SessionStatus />

                <!-- 导航标签 -->
                <div class="nav-tabs">
                  <n-tabs v-model:value="activeTab" type="line" size="small">
                    <n-tab-pane name="dashboard" tab="仪表板">
                      <!-- 快速操作栏 -->
                      <div class="quick-actions">
                        <n-space>
                          <n-button size="small" @click="handleQuickFill">
                            <n-icon><svg viewBox="0 0 24 24"><path fill="currentColor" d="M9,22A1,1 0 0,1 8,21V18H4A2,2 0 0,1 2,16V4C2,2.89 2.9,2 4,2H20A2,2 0 0,1 22,4V16A2,2 0 0,1 20,18H13.9L10.2,21.71C10,21.9 9.75,22 9.5,22V22H9M10,16V19.08L13.08,16H20V4H4V16H10Z" /></svg></n-icon>
                            自动填充
                          </n-button>
                          <n-button size="small" @click="handleGeneratePassword">
                            <n-icon><svg viewBox="0 0 24 24"><path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" /></svg></n-icon>
                            生成密码
                          </n-button>
                          <n-button size="small" @click="auth.logout">
                            <n-icon><svg viewBox="0 0 24 24"><path fill="currentColor" d="M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z" /></svg></n-icon>
                            登出
                          </n-button>
                        </n-space>
                      </div>

                      <!-- 统计信息 -->
                      <n-card size="small" class="stats-card">
                        <n-statistic label="总笔记数" :value="notesStore.getStats.total" />
                        <n-space style="margin-top: 8px;">
                          <n-tag size="small" type="warning">
                            收藏: {{ notesStore.getStats.favorites }}
                          </n-tag>
                          <n-tag size="small" type="info">
                            最近更新: {{ notesStore.recentNotes.length }}
                          </n-tag>
                        </n-space>
                      </n-card>

                      <!-- 最近笔记 -->
                      <n-card title="最近笔记" size="small" class="recent-notes">
                        <template #header-extra>
                          <n-button size="small" @click="activeTab = 'notes'">
                            查看全部
                          </n-button>
                        </template>
                        <div v-if="notesStore.recentNotes.length === 0" class="empty-state">
                          <n-empty description="暂无笔记">
                            <template #extra>
                              <n-button size="small" @click="activeTab = 'notes'">
                                创建第一个笔记
                              </n-button>
                            </template>
                          </n-empty>
                        </div>
                        <div v-else class="notes-list">
                          <div
                            v-for="note in notesStore.recentNotes.slice(0, 5)"
                            :key="note.id"
                            class="note-item"
                            @click="handleNoteClick(note)"
                          >
                            <div class="note-info">
                              <div class="note-title">{{ note.title }}</div>
                              <div class="note-meta">
                                <n-tag size="tiny" :type="getNoteTypeColor(note.note_type)">
                                  {{ getNoteTypeLabel(note.note_type) }}
                                </n-tag>
                                <span class="note-time">
                                  {{ formatTime(note.updated_at) }}
                                </span>
                              </div>
                            </div>
                            <n-icon v-if="note.is_favorite" size="16" color="#f0a020">
                              <svg viewBox="0 0 24 24"><path fill="currentColor" d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.45,13.97L5.82,21L12,17.27Z" /></svg>
                            </n-icon>
                          </div>
                        </div>
                      </n-card>
                    </n-tab-pane>

                    <n-tab-pane name="notes" tab="笔记管理">
                      <NotesManager />
                    </n-tab-pane>
                  </n-tabs>
                </div>
              </div>
            </main>
          </div>
        </n-loading-bar-provider>
      </n-message-provider>
    </n-config-provider>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  NConfigProvider,
  NMessageProvider,
  NLoadingBarProvider,
  NCard,
  NButton,
  NIcon,
  NSpace,

  NSpin,
  NStatistic,
  NTag,
  NEmpty,
  NTabs,
  NTabPane,
  darkTheme,
  useMessage
} from 'naive-ui';
import { useAuthStore } from '../../stores/auth';
import { useNotesStore } from '../../stores/notes';
import { sendToActiveTab, MessageType } from '../../utils/messaging';
import { useAuth } from '../../composables/useAuth';
import AuthContainer from '../../components/auth/AuthContainer.vue';
import SessionStatus from '../../components/auth/SessionStatus.vue';
import NotesManager from '../../components/notes/NotesManager.vue';
import type { NoteType } from '../../types';

// 状态管理
const authStore = useAuthStore();
const notesStore = useNotesStore();
const message = useMessage();
const auth = useAuth();

// 主题管理
const isDark = ref(false);
const theme = computed(() => isDark.value ? darkTheme : null);

// 初始化状态
const isInitializing = ref(true);

// 导航状态
const activeTab = ref('dashboard');

// 移除了旧的认证表单状态，现在使用AuthContainer组件

// 初始化
onMounted(async () => {
  try {
    // 使用新的认证组合函数初始化
    await auth.initialize();

    // 如果已认证，初始化笔记数据
    if (auth.isAuthenticated.value) {
      await notesStore.initialize();
    }
  } catch (error) {
    console.error('Failed to initialize app:', error);
    message.error('初始化失败');
  } finally {
    isInitializing.value = false;
  }
});

// 主题切换
const toggleTheme = () => {
  isDark.value = !isDark.value;
  // TODO: 保存主题设置到存储
};

// 移除了旧的登录注册处理方法，现在由AuthContainer组件处理

// 快速填充
const handleQuickFill = async () => {
  try {
    const response = await sendToActiveTab(MessageType.DETECT_FORMS);

    if (response.success && response.data?.length > 0) {
      // TODO: 显示匹配的登录凭据选择界面
      message.info('检测到登录表单，功能开发中...');
    } else {
      message.warning('当前页面未检测到登录表单');
    }
  } catch (error) {
    console.error('Quick fill failed:', error);
    message.error('自动填充失败');
  }
};

// 生成密码
const handleGeneratePassword = () => {
  // TODO: 打开密码生成器
  message.info('密码生成器功能开发中...');
};

// 笔记点击 - 切换到笔记管理标签
const handleNoteClick = (note: any) => {
  activeTab.value = 'notes';
  // TODO: 在NotesManager中打开特定笔记
};

// 获取笔记类型颜色
const getNoteTypeColor = (type: NoteType): string => {
  const colors: Record<NoteType, string> = {
    [NoteType.Login]: 'success',
    [NoteType.SshKey]: 'info',
    [NoteType.ApiKey]: 'warning',
    [NoteType.Certificate]: 'error',
    [NoteType.Database]: 'primary',
    [NoteType.Totp]: 'success',
    [NoteType.TechNote]: 'default',
  };
  return colors[type] || 'default';
};

// 获取笔记类型标签
const getNoteTypeLabel = (type: NoteType): string => {
  const labels: Record<NoteType, string> = {
    [NoteType.Login]: '登录',
    [NoteType.SshKey]: 'SSH',
    [NoteType.ApiKey]: 'API',
    [NoteType.Certificate]: '证书',
    [NoteType.Database]: '数据库',
    [NoteType.Totp]: 'TOTP',
    [NoteType.TechNote]: '笔记',
  };
  return labels[type] || type;
};

// 格式化时间
const formatTime = (timeStr: string): string => {
  const time = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - time.getTime();

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;

  return time.toLocaleDateString();
};
</script>

<style scoped>
.app {
  width: 380px;
  height: 600px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.icon {
  color: #18a058;
}

.main {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.welcome {
  text-align: center;
  padding: 32px 16px;
}

.welcome h2 {
  margin: 16px 0 8px;
  font-size: 20px;
  font-weight: 600;
}

.welcome p {
  margin: 0 0 24px;
  color: #666;
  font-size: 14px;
}
</style>
