<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "技术笔记编辑器组件完整，支持代码高亮和分类管理"
}} -->
<template>
  <div class="tech-note-editor">
    <n-space vertical size="medium">
      <!-- 分类和语言 -->
      <n-grid :cols="2" :x-gap="16">
        <n-grid-item>
          <n-form-item label="分类" path="category">
            <n-select
              v-model:value="localValue.category"
              :options="categoryOptions"
              placeholder="选择分类"
              :disabled="disabled"
            />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="编程语言" path="language">
            <n-select
              v-model:value="localValue.language"
              :options="languageOptions"
              placeholder="选择语言"
              :disabled="disabled"
              filterable
              tag
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>

      <!-- 环境 -->
      <n-form-item label="适用环境" path="environment">
        <n-input
          v-model:value="localValue.environment"
          placeholder="如：Linux, macOS, Docker, Kubernetes 等"
          :disabled="disabled"
        />
      </n-form-item>

      <!-- 内容 -->
      <n-form-item label="笔记内容" path="content">
        <div class="content-editor">
          <!-- 工具栏 -->
          <div class="editor-toolbar">
            <n-space size="small">
              <n-button
                size="small"
                @click="insertTemplate('command')"
                :disabled="disabled"
              >
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M20,19V7H4V19H20M20,3A2,2 0 0,1 22,5V19A2,2 0 0,1 20,21H4A2,2 0 0,1 2,19V5A2,2 0 0,1 4,3H20M13,17V15H18V17H13M9.58,13L5.57,9H8.4L11.7,12.3C12.09,12.69 12.09,13.33 11.7,13.72L8.42,17H5.59L9.58,13Z" />
                  </svg>
                </n-icon>
                命令
              </n-button>
              <n-button
                size="small"
                @click="insertTemplate('code')"
                :disabled="disabled"
              >
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M14.6,16.6L19.2,12L14.6,7.4L16,6L22,12L16,18L14.6,16.6M9.4,16.6L4.8,12L9.4,7.4L8,6L2,12L8,18L9.4,16.6Z" />
                  </svg>
                </n-icon>
                代码块
              </n-button>
              <n-button
                size="small"
                @click="insertTemplate('config')"
                :disabled="disabled"
              >
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" />
                  </svg>
                </n-icon>
                配置
              </n-button>
            </n-space>
          </div>

          <!-- 文本编辑器 -->
          <n-input
            ref="textareaRef"
            v-model:value="localValue.content"
            type="textarea"
            placeholder="输入您的技术笔记内容..."
            :rows="12"
            :disabled="disabled"
            show-count
            :maxlength="10000"
            @keydown="handleKeydown"
          />
        </div>
      </n-form-item>

      <!-- 参考链接 -->
      <n-form-item label="参考链接" path="references">
        <n-dynamic-input
          v-model:value="localValue.references"
          placeholder="添加参考链接..."
          :disabled="disabled"
          :max="10"
        >
          <template #default="{ value }">
            <n-input
              v-model:value="value.value"
              placeholder="https://example.com"
              @blur="validateReference(value)"
            />
          </template>
        </n-dynamic-input>
      </n-form-item>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import {
  NSpace,
  NFormItem,
  NInput,
  NSelect,
  NButton,
  NIcon,
  NGrid,
  NGridItem,
  NDynamicInput,
  useMessage
} from 'naive-ui';
import type { TechNoteContent } from '../../../types';

// Props & Emits
interface Props {
  value: TechNoteContent;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});

const emit = defineEmits<{
  'update:value': [value: TechNoteContent];
}>();

// 状态
const textareaRef = ref();
const message = useMessage();

// 本地值
const localValue = ref<TechNoteContent>({ ...props.value });

// 选项
const categoryOptions = [
  { label: '文档', value: 'documentation' },
  { label: '命令', value: 'command' },
  { label: '代码片段', value: 'snippet' },
  { label: '配置', value: 'config' },
  { label: '故障排除', value: 'troubleshooting' },
  { label: '最佳实践', value: 'best-practice' },
  { label: '教程', value: 'tutorial' },
];

const languageOptions = [
  { label: 'JavaScript', value: 'javascript' },
  { label: 'TypeScript', value: 'typescript' },
  { label: 'Python', value: 'python' },
  { label: 'Java', value: 'java' },
  { label: 'Go', value: 'go' },
  { label: 'Rust', value: 'rust' },
  { label: 'C++', value: 'cpp' },
  { label: 'C#', value: 'csharp' },
  { label: 'PHP', value: 'php' },
  { label: 'Ruby', value: 'ruby' },
  { label: 'Shell', value: 'shell' },
  { label: 'SQL', value: 'sql' },
  { label: 'HTML', value: 'html' },
  { label: 'CSS', value: 'css' },
  { label: 'JSON', value: 'json' },
  { label: 'YAML', value: 'yaml' },
  { label: 'Markdown', value: 'markdown' },
];

// 模板
const templates = {
  command: `# 命令说明

## 用途
描述命令的用途

## 语法
\`\`\`bash
command [options] [arguments]
\`\`\`

## 示例
\`\`\`bash
# 示例1：基本用法
command example

# 示例2：带参数
command -option value
\`\`\`

## 注意事项
- 注意事项1
- 注意事项2
`,
  code: `# 代码片段

## 功能描述
描述代码的功能

## 代码
\`\`\`${localValue.value.language || 'javascript'}
// 在这里添加代码
function example() {
    console.log('Hello World');
}
\`\`\`

## 使用方法
1. 步骤1
2. 步骤2
3. 步骤3
`,
  config: `# 配置文件

## 配置说明
描述配置的用途和作用

## 配置内容
\`\`\`${localValue.value.language || 'yaml'}
# 配置示例
key: value
nested:
  key: value
\`\`\`

## 配置位置
- 配置文件路径：\`/path/to/config\`
- 重启服务：\`sudo systemctl restart service\`
`
};

// 方法
const insertTemplate = (type: keyof typeof templates) => {
  const template = templates[type];
  const textarea = textareaRef.value?.textareaElRef;
  
  if (textarea) {
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentValue = localValue.value.content || '';
    
    const newValue = currentValue.substring(0, start) + 
                    template + 
                    currentValue.substring(end);
    
    localValue.value.content = newValue;
    
    // 设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + template.length, start + template.length);
    }, 0);
  } else {
    // 如果无法获取textarea引用，直接追加
    localValue.value.content = (localValue.value.content || '') + '\n\n' + template;
  }
};

const handleKeydown = (event: KeyboardEvent) => {
  // Tab键插入缩进
  if (event.key === 'Tab') {
    event.preventDefault();
    const textarea = event.target as HTMLTextAreaElement;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    
    const value = textarea.value;
    const newValue = value.substring(0, start) + '  ' + value.substring(end);
    
    localValue.value.content = newValue;
    
    setTimeout(() => {
      textarea.setSelectionRange(start + 2, start + 2);
    }, 0);
  }
};

const validateReference = (ref: { value: string }) => {
  if (ref.value) {
    try {
      new URL(ref.value);
    } catch {
      message.warning('请输入有效的URL地址');
    }
  }
};

// 监听变化
watch(localValue, (newValue) => {
  emit('update:value', { ...newValue });
}, { deep: true });

watch(() => props.value, (newValue) => {
  localValue.value = { ...newValue };
}, { deep: true });
</script>

<style scoped>
.tech-note-editor {
  width: 100%;
}

.content-editor {
  border: 1px solid var(--n-border-color);
  border-radius: 6px;
  overflow: hidden;
}

.editor-toolbar {
  padding: 8px 12px;
  background: var(--n-card-color);
  border-bottom: 1px solid var(--n-border-color);
}

:deep(.n-input-wrapper) {
  border: none;
  border-radius: 0;
}

:deep(.n-input__textarea) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

:deep(.n-input__count) {
  background: var(--n-card-color);
  border-top: 1px solid var(--n-border-color);
}
</style>
