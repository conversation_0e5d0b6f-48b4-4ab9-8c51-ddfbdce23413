// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#b290202c-b04c-4580-9b7a-7c86848ed54f"
//   Timestamp: "2025-08-05T17:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "认证组合函数完整，支持会话管理和自动锁定"
// }}
// 认证相关组合函数

import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '../stores/auth';
import { ActivityStorage, SettingsStorage } from '../utils/storage';
import { useMessage } from 'naive-ui';

export function useAuth() {
  const authStore = useAuthStore();
  const message = useMessage();

  // 响应式状态
  const isInitialized = ref(false);
  const sessionTimeRemaining = ref(0);
  const isSessionWarningShown = ref(false);

  // 计算属性
  const isAuthenticated = computed(() => authStore.isAuthenticated);
  const user = computed(() => authStore.user);
  const isLoading = computed(() => authStore.isLoading);

  // 定时器引用
  let sessionCheckInterval: number | null = null;
  let sessionWarningTimeout: number | null = null;
  let activityUpdateTimeout: number | null = null;

  // 初始化认证状态
  const initialize = async () => {
    try {
      await authStore.initialize();
      isInitialized.value = true;
      
      if (authStore.isAuthenticated) {
        startSessionMonitoring();
        setupActivityTracking();
      }
    } catch (error) {
      console.error('Failed to initialize auth:', error);
      message.error('认证初始化失败');
    }
  };

  // 登录
  const login = async (credentials: { email: string; master_password: string }) => {
    const success = await authStore.login(credentials);
    
    if (success) {
      startSessionMonitoring();
      setupActivityTracking();
    }
    
    return success;
  };

  // 注册
  const register = async (userData: { email: string; master_password: string }) => {
    const success = await authStore.register(userData);
    
    if (success) {
      startSessionMonitoring();
      setupActivityTracking();
    }
    
    return success;
  };

  // 登出
  const logout = async () => {
    stopSessionMonitoring();
    stopActivityTracking();
    await authStore.logout();
    isSessionWarningShown.value = false;
  };

  // 开始会话监控
  const startSessionMonitoring = async () => {
    const settings = await SettingsStorage.getSettings();
    
    if (!settings.autoLock) {
      return;
    }

    // 每30秒检查一次会话状态
    sessionCheckInterval = window.setInterval(async () => {
      const shouldLock = await ActivityStorage.shouldAutoLock();
      
      if (shouldLock) {
        await handleAutoLock();
        return;
      }

      // 计算剩余时间
      const lastActivity = await ActivityStorage.getLastActivity();
      const now = Date.now();
      const timeoutMs = settings.autoLockTimeout * 60 * 1000;
      const elapsed = now - lastActivity;
      const remaining = Math.max(0, timeoutMs - elapsed);
      
      sessionTimeRemaining.value = Math.floor(remaining / 1000);

      // 如果剩余时间少于2分钟，显示警告
      if (remaining <= 120000 && remaining > 0 && !isSessionWarningShown.value) {
        showSessionWarning();
      }
    }, 30000);
  };

  // 停止会话监控
  const stopSessionMonitoring = () => {
    if (sessionCheckInterval) {
      clearInterval(sessionCheckInterval);
      sessionCheckInterval = null;
    }
    
    if (sessionWarningTimeout) {
      clearTimeout(sessionWarningTimeout);
      sessionWarningTimeout = null;
    }
    
    sessionTimeRemaining.value = 0;
    isSessionWarningShown.value = false;
  };

  // 设置活动追踪
  const setupActivityTracking = () => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    const updateActivity = () => {
      if (activityUpdateTimeout) {
        clearTimeout(activityUpdateTimeout);
      }
      
      activityUpdateTimeout = window.setTimeout(() => {
        ActivityStorage.updateLastActivity();
        
        // 重置会话警告状态
        if (isSessionWarningShown.value) {
          isSessionWarningShown.value = false;
        }
      }, 1000); // 防抖1秒
    };

    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    // 存储事件监听器引用以便清理
    (window as any).__authActivityListeners = events.map(event => ({
      event,
      handler: updateActivity,
    }));
  };

  // 停止活动追踪
  const stopActivityTracking = () => {
    const listeners = (window as any).__authActivityListeners;
    
    if (listeners) {
      listeners.forEach(({ event, handler }: any) => {
        document.removeEventListener(event, handler, true);
      });
      delete (window as any).__authActivityListeners;
    }
    
    if (activityUpdateTimeout) {
      clearTimeout(activityUpdateTimeout);
      activityUpdateTimeout = null;
    }
  };

  // 处理自动锁定
  const handleAutoLock = async () => {
    stopSessionMonitoring();
    stopActivityTracking();
    
    await authStore.logout();
    
    message.warning('会话已过期，请重新登录', {
      duration: 5000,
    });
  };

  // 显示会话警告
  const showSessionWarning = () => {
    isSessionWarningShown.value = true;
    
    message.warning('会话即将过期，请进行操作以保持登录状态', {
      duration: 10000,
      closable: true,
    });

    // 2分钟后如果还没有活动，自动登出
    sessionWarningTimeout = window.setTimeout(async () => {
      if (isSessionWarningShown.value) {
        await handleAutoLock();
      }
    }, 120000);
  };

  // 延长会话
  const extendSession = async () => {
    await ActivityStorage.updateLastActivity();
    isSessionWarningShown.value = false;
    
    if (sessionWarningTimeout) {
      clearTimeout(sessionWarningTimeout);
      sessionWarningTimeout = null;
    }
    
    message.success('会话已延长');
  };

  // 检查密码强度
  const checkPasswordStrength = (password: string) => {
    return authStore.validatePassword(password);
  };

  // 检查邮箱格式
  const checkEmailFormat = (email: string) => {
    return authStore.validateEmail(email);
  };

  // 获取会话信息
  const getSessionInfo = async () => {
    const lastActivity = await ActivityStorage.getLastActivity();
    const settings = await SettingsStorage.getSettings();
    
    return {
      lastActivity: new Date(lastActivity),
      autoLockEnabled: settings.autoLock,
      autoLockTimeout: settings.autoLockTimeout,
      timeRemaining: sessionTimeRemaining.value,
    };
  };

  // 监听来自background的自动锁定消息
  const setupBackgroundListener = () => {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'AUTO_LOCK_TRIGGERED') {
        handleAutoLock();
      }
    });
  };

  // 生命周期管理
  onMounted(() => {
    setupBackgroundListener();
  });

  onUnmounted(() => {
    stopSessionMonitoring();
    stopActivityTracking();
  });

  return {
    // 状态
    isInitialized,
    isAuthenticated,
    user,
    isLoading,
    sessionTimeRemaining,
    isSessionWarningShown,
    
    // 方法
    initialize,
    login,
    register,
    logout,
    extendSession,
    checkPasswordStrength,
    checkEmailFormat,
    getSessionInfo,
  };
}

// 密码强度评估工具
export function usePasswordStrength() {
  const evaluateStrength = (password: string) => {
    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };

    const score = Object.values(checks).filter(Boolean).length;
    
    let level: 'weak' | 'medium' | 'strong' | 'very-strong';
    let text: string;
    let percentage: number;

    if (score <= 2) {
      level = 'weak';
      text = '密码强度：弱';
      percentage = 25;
    } else if (score === 3) {
      level = 'medium';
      text = '密码强度：中等';
      percentage = 50;
    } else if (score === 4) {
      level = 'strong';
      text = '密码强度：强';
      percentage = 75;
    } else {
      level = 'very-strong';
      text = '密码强度：很强';
      percentage = 100;
    }

    return {
      checks,
      level,
      text,
      percentage,
      score,
    };
  };

  const generateStrongPassword = (length: number = 16) => {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const special = '!@#$%^&*(),.?":{}|<>';
    
    const allChars = uppercase + lowercase + numbers + special;
    
    let password = '';
    
    // 确保包含每种类型的字符
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += special[Math.floor(Math.random() * special.length)];
    
    // 填充剩余长度
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // 打乱字符顺序
    return password.split('').sort(() => Math.random() - 0.5).join('');
  };

  return {
    evaluateStrength,
    generateStrongPassword,
  };
}
