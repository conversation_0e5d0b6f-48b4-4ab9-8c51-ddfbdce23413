<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "笔记编辑器组件完整，支持多种笔记类型的编辑"
}} -->
<template>
  <div class="note-editor">
    <n-card>
      <template #header>
        <n-space justify="space-between" align="center">
          <div class="editor-title">
            <n-icon :size="20" :color="getNoteTypeColor(formData.note_type)">
              <component :is="getNoteTypeIcon(formData.note_type)" />
            </n-icon>
            <span>{{ isEditing ? '编辑笔记' : '创建笔记' }}</span>
          </div>
          <n-space>
            <n-button @click="$emit('cancel')">
              取消
            </n-button>
            <n-button 
              type="primary" 
              :loading="isLoading"
              @click="handleSave"
            >
              {{ isEditing ? '保存' : '创建' }}
            </n-button>
          </n-space>
        </n-space>
      </template>

      <n-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules"
        label-placement="top"
        size="medium"
      >
        <!-- 基础信息 -->
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item path="title" label="笔记标题">
              <n-input
                v-model:value="formData.title"
                placeholder="请输入笔记标题"
                :disabled="isLoading"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item path="note_type" label="笔记类型">
              <n-select
                v-model:value="formData.note_type"
                :options="noteTypeOptions"
                placeholder="选择笔记类型"
                :disabled="isEditing || isLoading"
                @update:value="handleTypeChange"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <!-- 标签 -->
        <n-form-item path="tags" label="标签">
          <n-dynamic-tags
            v-model:value="formData.tags"
            :max="10"
            :disabled="isLoading"
          />
        </n-form-item>

        <!-- 收藏 -->
        <n-form-item>
          <n-checkbox 
            v-model:checked="formData.is_favorite"
            :disabled="isLoading"
          >
            添加到收藏
          </n-checkbox>
        </n-form-item>

        <!-- 内容编辑器 -->
        <n-form-item path="content" label="笔记内容">
          <div class="content-editor">
            <!-- 登录类型编辑器 -->
            <LoginEditor
              v-if="formData.note_type === 'login'"
              v-model:value="formData.content"
              :disabled="isLoading"
            />

            <!-- SSH密钥类型编辑器 -->
            <SshKeyEditor
              v-else-if="formData.note_type === 'ssh_key'"
              v-model:value="formData.content"
              :disabled="isLoading"
            />

            <!-- API密钥类型编辑器 -->
            <ApiKeyEditor
              v-else-if="formData.note_type === 'api_key'"
              v-model:value="formData.content"
              :disabled="isLoading"
            />

            <!-- 证书类型编辑器 -->
            <CertificateEditor
              v-else-if="formData.note_type === 'certificate'"
              v-model:value="formData.content"
              :disabled="isLoading"
            />

            <!-- 数据库类型编辑器 -->
            <DatabaseEditor
              v-else-if="formData.note_type === 'database'"
              v-model:value="formData.content"
              :disabled="isLoading"
            />

            <!-- TOTP类型编辑器 -->
            <TotpEditor
              v-else-if="formData.note_type === 'totp'"
              v-model:value="formData.content"
              :disabled="isLoading"
            />

            <!-- 技术笔记类型编辑器 -->
            <TechNoteEditor
              v-else-if="formData.note_type === 'tech_note'"
              v-model:value="formData.content"
              :disabled="isLoading"
            />

            <!-- 默认编辑器 -->
            <div v-else class="default-editor">
              <n-alert type="info">
                请先选择笔记类型
              </n-alert>
            </div>
          </div>
        </n-form-item>
      </n-form>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, h } from 'vue';
import {
  NCard,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NButton,
  NIcon,
  NSpace,
  NGrid,
  NGridItem,
  NDynamicTags,
  NCheckbox,
  NAlert,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui';
import { useNotesStore } from '../../stores/notes';
import LoginEditor from './editors/LoginEditor.vue';
import SshKeyEditor from './editors/SshKeyEditor.vue';
import ApiKeyEditor from './editors/ApiKeyEditor.vue';
import CertificateEditor from './editors/CertificateEditor.vue';
import DatabaseEditor from './editors/DatabaseEditor.vue';
import TotpEditor from './editors/TotpEditor.vue';
import TechNoteEditor from './editors/TechNoteEditor.vue';
import type { SecureNote, CreateNoteRequest, UpdateNoteRequest, NoteType } from '../../types';

// Props & Emits
interface Props {
  note?: SecureNote;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<{
  save: [data: CreateNoteRequest | UpdateNoteRequest];
  cancel: [];
}>();

// 状态管理
const notesStore = useNotesStore();
const message = useMessage();

// 响应式状态
const formRef = ref<FormInst | null>(null);
const isLoading = computed(() => props.loading || notesStore.isLoading);
const isEditing = computed(() => !!props.note);

// 表单数据
const formData = reactive<CreateNoteRequest>({
  title: '',
  note_type: 'login',
  content: {},
  tags: [],
  is_favorite: false,
});

// 笔记类型选项
const noteTypeOptions = [
  { label: '登录凭据', value: 'login' },
  { label: 'SSH密钥', value: 'ssh_key' },
  { label: 'API密钥', value: 'api_key' },
  { label: '证书', value: 'certificate' },
  { label: '数据库', value: 'database' },
  { label: 'TOTP', value: 'totp' },
  { label: '技术笔记', value: 'tech_note' },
];

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入笔记标题', trigger: 'blur' },
    { min: 1, max: 100, message: '标题长度应在1-100字符之间', trigger: 'blur' },
  ],
  note_type: [
    { required: true, message: '请选择笔记类型', trigger: 'change' },
  ],
  content: [
    { required: true, message: '请填写笔记内容', trigger: 'blur' },
  ],
};

// 方法
const getNoteTypeColor = (type: NoteType): string => {
  const colors: Record<NoteType, string> = {
    login: '#18a058',
    ssh_key: '#2080f0',
    api_key: '#f0a020',
    certificate: '#d03050',
    database: '#7c3aed',
    totp: '#059669',
    tech_note: '#6b7280',
  };
  return colors[type] || '#6b7280';
};

const getNoteTypeIcon = (type: NoteType) => {
  // 简化处理，返回通用图标
  return () => h('svg', { viewBox: '0 0 24 24' }, [
    h('path', { fill: 'currentColor', d: 'M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z' })
  ]);
};

const handleTypeChange = (type: NoteType) => {
  // 重置内容
  formData.content = getDefaultContent(type);
};

const getDefaultContent = (type: NoteType) => {
  switch (type) {
    case 'login':
      return { url: '', username: '', password: '', notes: '' };
    case 'ssh_key':
      return { key_type: 'ed25519', private_key: '', public_key: '', servers: [] };
    case 'api_key':
      return { service: '', api_key: '', permissions: [] };
    case 'certificate':
      return { certificate: '', private_key: '', domain: '', expires_at: '', certificate_type: 'ssl' };
    case 'database':
      return { host: '', port: 5432, database: '', username: '', password: '', db_type: 'postgresql', environment: 'dev', ssl_required: false };
    case 'totp':
      return { secret: '', issuer: '', account: '', algorithm: 'SHA1', digits: 6, period: 30 };
    case 'tech_note':
      return { content: '', category: 'documentation', references: [] };
    default:
      return {};
  }
};

const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    
    if (isEditing.value) {
      // 更新笔记
      const updateData: UpdateNoteRequest = {
        title: formData.title,
        content: formData.content,
        tags: formData.tags,
        is_favorite: formData.is_favorite,
      };
      emit('save', updateData);
    } else {
      // 创建笔记
      emit('save', formData);
    }
  } catch (error) {
    console.error('Form validation failed:', error);
    message.error('请检查表单内容');
  }
};

// 监听props变化
watch(() => props.note, (note) => {
  if (note) {
    // 编辑模式，填充表单数据
    Object.assign(formData, {
      title: note.title,
      note_type: note.note_type,
      content: { ...note.content },
      tags: [...note.tags],
      is_favorite: note.is_favorite,
    });
  } else {
    // 创建模式，重置表单
    Object.assign(formData, {
      title: '',
      note_type: 'login',
      content: getDefaultContent('login'),
      tags: [],
      is_favorite: false,
    });
  }
}, { immediate: true });

// 生命周期
onMounted(() => {
  // 初始化默认内容
  if (!props.note) {
    formData.content = getDefaultContent(formData.note_type);
  }
});
</script>

<style scoped>
.note-editor {
  height: 100%;
  overflow: hidden;
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
}

.content-editor {
  min-height: 300px;
}

.default-editor {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

:deep(.n-card-header) {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--n-border-color);
}

:deep(.n-form-item-label) {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }
}
</style>
