// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#8db4a073-e2f1-4c70-9e28-e31d3e70f268"
//   Timestamp: "2025-08-05T15:30:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "JWT中间件实现完整，支持token验证和用户信息提取"
// }}
// JWT认证中间件实现

use axum::{
    extract::{Request, Extension},
    http::{StatusCode, HeaderMap},
    middleware::Next,
    response::Response,
};
use tracing::{warn, debug};

use super::models::{Claims, AuthError};
use super::handlers::AuthService;
use shared::AppError;

/// JWT认证中间件
pub async fn jwt_auth_middleware(
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // 从配置中获取JWT密钥
    let config = match crate::config::Config::load() {
        Ok(config) => config,
        Err(_) => return Err(StatusCode::INTERNAL_SERVER_ERROR),
    };
    let auth_service = AuthService::new(config.jwt_secret);

    let headers = request.headers();
    // 从Authorization header中提取token
    let token = match extract_token_from_headers(headers) {
        Some(token) => token,
        None => {
            warn!("Missing or invalid Authorization header");
            return Err(StatusCode::UNAUTHORIZED);
        }
    };

    // 验证token
    let claims = match auth_service.verify_token(&token) {
        Ok(claims) => claims,
        Err(AuthError::TokenExpired) => {
            warn!("Token expired");
            return Err(StatusCode::UNAUTHORIZED);
        }
        Err(AuthError::InvalidToken) | Err(AuthError::Jwt(_)) => {
            warn!("Invalid token");
            return Err(StatusCode::UNAUTHORIZED);
        }
        Err(e) => {
            warn!("Token verification error: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    debug!("JWT authentication successful for user: {}", claims.email);

    // 将claims添加到请求扩展中，供后续处理器使用
    request.extensions_mut().insert(claims);

    // 继续处理请求
    Ok(next.run(request).await)
}

/// 可选的JWT认证中间件（不强制要求认证）
pub async fn optional_jwt_auth_middleware(
    mut request: Request,
    next: Next,
) -> Response {
    // 从请求扩展中获取AuthService
    if let Some(auth_service) = request.extensions().get::<AuthService>() {
        let headers = request.headers();

        // 尝试从Authorization header中提取token
        if let Some(token) = extract_token_from_headers(headers) {
            // 尝试验证token
            if let Ok(claims) = auth_service.verify_token(&token) {
                debug!("Optional JWT authentication successful for user: {}", claims.email);
                request.extensions_mut().insert(claims);
            } else {
                debug!("Optional JWT authentication failed, continuing without auth");
            }
        }
    }

    // 无论认证是否成功都继续处理请求
    next.run(request).await
}

/// 从请求头中提取Bearer token
fn extract_token_from_headers(headers: &HeaderMap) -> Option<String> {
    let auth_header = headers.get("authorization")?;
    let auth_str = auth_header.to_str().ok()?;

    if auth_str.starts_with("Bearer ") {
        Some(auth_str[7..].to_string())
    } else {
        None
    }
}

/// 从请求扩展中获取当前用户Claims
pub fn get_current_user(request: &Request) -> Result<&Claims, AppError> {
    request.extensions()
        .get::<Claims>()
        .ok_or_else(|| AppError::Unauthorized("Authentication required".to_string()))
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::HeaderValue;

    #[test]
    fn test_extract_token_from_headers() {
        let mut headers = HeaderMap::new();

        // 测试有效的Bearer token
        headers.insert(
            "authorization",
            HeaderValue::from_static("Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9")
        );

        let token = extract_token_from_headers(&headers);
        assert_eq!(token, Some("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9".to_string()));

        // 测试无效格式
        headers.insert(
            "authorization",
            HeaderValue::from_static("Basic dXNlcjpwYXNz")
        );

        let token = extract_token_from_headers(&headers);
        assert_eq!(token, None);

        // 测试缺失header
        headers.clear();
        let token = extract_token_from_headers(&headers);
        assert_eq!(token, None);
    }
}
