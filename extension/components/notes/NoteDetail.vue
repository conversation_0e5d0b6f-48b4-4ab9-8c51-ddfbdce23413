<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "笔记详情组件完整，支持内容展示和快速操作"
}} -->
<template>
  <div class="note-detail">
    <n-card>
      <!-- 头部 -->
      <template #header>
        <n-space justify="space-between" align="center">
          <div class="note-header">
            <n-space align="center">
              <n-button text @click="$emit('close')">
                <n-icon size="20">
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" />
                  </svg>
                </n-icon>
              </n-button>
              <n-icon :size="24" :color="getNoteTypeColor(note.note_type)">
                <component :is="getNoteTypeIcon(note.note_type)" />
              </n-icon>
              <div>
                <h2 class="note-title">{{ note.title }}</h2>
                <n-space size="small">
                  <n-tag :type="getNoteTypeTagType(note.note_type)" size="small">
                    {{ getNoteTypeLabel(note.note_type) }}
                  </n-tag>
                  <n-icon v-if="note.is_favorite" size="16" color="#f0a020">
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.45,13.97L5.82,21L12,17.27Z" />
                    </svg>
                  </n-icon>
                </n-space>
              </div>
            </n-space>
          </div>
          
          <n-space>
            <n-button @click="$emit('edit', note)">
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z" />
                </svg>
              </n-icon>
              编辑
            </n-button>
            <n-dropdown :options="actionOptions" @select="handleAction">
              <n-button>
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z" />
                  </svg>
                </n-icon>
              </n-button>
            </n-dropdown>
          </n-space>
        </n-space>
      </template>

      <!-- 内容 -->
      <div class="note-content">
        <!-- 基本信息 -->
        <div class="info-section">
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="创建时间">
              {{ formatTime(note.created_at) }}
            </n-descriptions-item>
            <n-descriptions-item label="更新时间">
              {{ formatTime(note.updated_at) }}
            </n-descriptions-item>
          </n-descriptions>
        </div>

        <!-- 标签 -->
        <div v-if="note.tags.length > 0" class="tags-section">
          <h3>标签</h3>
          <n-space>
            <n-tag
              v-for="tag in note.tags"
              :key="tag"
              type="default"
              size="small"
            >
              {{ tag }}
            </n-tag>
          </n-space>
        </div>

        <!-- 内容详情 -->
        <div class="content-section">
          <h3>详细信息</h3>
          
          <!-- 登录类型 -->
          <div v-if="note.note_type === 'login'" class="login-content">
            <n-descriptions bordered>
              <n-descriptions-item label="网站">
                <n-space align="center">
                  <span>{{ note.content.url }}</span>
                  <n-button
                    v-if="note.content.url"
                    text
                    size="small"
                    @click="openUrl(note.content.url)"
                  >
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z" />
                      </svg>
                    </n-icon>
                  </n-button>
                </n-space>
              </n-descriptions-item>
              <n-descriptions-item label="用户名">
                <n-space align="center">
                  <span>{{ note.content.username }}</span>
                  <n-button
                    text
                    size="small"
                    @click="copyToClipboard(note.content.username)"
                  >
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" />
                      </svg>
                    </n-icon>
                  </n-button>
                </n-space>
              </n-descriptions-item>
              <n-descriptions-item label="密码">
                <n-space align="center">
                  <span v-if="showPassword">{{ note.content.password }}</span>
                  <span v-else>••••••••</span>
                  <n-button
                    text
                    size="small"
                    @click="showPassword = !showPassword"
                  >
                    <n-icon>
                      <svg v-if="showPassword" viewBox="0 0 24 24">
                        <path fill="currentColor" d="M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z" />
                      </svg>
                      <svg v-else viewBox="0 0 24 24">
                        <path fill="currentColor" d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z" />
                      </svg>
                    </n-icon>
                  </n-button>
                  <n-button
                    text
                    size="small"
                    @click="copyToClipboard(note.content.password)"
                  >
                    <n-icon>
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" />
                      </svg>
                    </n-icon>
                  </n-button>
                </n-space>
              </n-descriptions-item>
              <n-descriptions-item v-if="note.content.notes" label="备注">
                {{ note.content.notes }}
              </n-descriptions-item>
            </n-descriptions>
          </div>

          <!-- TOTP类型 -->
          <div v-else-if="note.note_type === 'totp'" class="totp-content">
            <n-descriptions bordered>
              <n-descriptions-item label="发行方">
                {{ note.content.issuer }}
              </n-descriptions-item>
              <n-descriptions-item label="账户">
                {{ note.content.account }}
              </n-descriptions-item>
              <n-descriptions-item label="当前验证码">
                <div v-if="totpCode" class="totp-code">
                  <n-space align="center">
                    <span class="code">{{ totpCode.code }}</span>
                    <n-progress
                      type="circle"
                      :percentage="totpCode.percentage"
                      :size="24"
                      :show-indicator="false"
                      :color="getTotpColor(totpCode.remaining)"
                    />
                    <span class="remaining">{{ totpCode.remaining }}s</span>
                    <n-button
                      text
                      size="small"
                      @click="copyToClipboard(totpCode.code)"
                    >
                      <n-icon>
                        <svg viewBox="0 0 24 24">
                          <path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" />
                        </svg>
                      </n-icon>
                    </n-button>
                  </n-space>
                </div>
                <n-button v-else @click="generateTotp">
                  生成验证码
                </n-button>
              </n-descriptions-item>
            </n-descriptions>
          </div>

          <!-- 其他类型的简化显示 -->
          <div v-else class="generic-content">
            <n-code :code="JSON.stringify(note.content, null, 2)" language="json" />
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, h } from 'vue';
import {
  NCard,
  NSpace,
  NButton,
  NIcon,
  NTag,
  NDropdown,
  NDescriptions,
  NDescriptionsItem,
  NProgress,
  NCode,
  useMessage
} from 'naive-ui';
import { useNotesStore } from '../../stores/notes';
import type { SecureNote, NoteType } from '../../types';

// Props & Emits
interface Props {
  note: SecureNote;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  edit: [note: SecureNote];
  delete: [note: SecureNote];
  close: [];
}>();

// 状态管理
const notesStore = useNotesStore();
const message = useMessage();

// 响应式状态
const showPassword = ref(false);
const totpCode = ref<{ code: string; remaining: number; percentage: number } | null>(null);

// 操作选项
const actionOptions = [
  { label: '复制全部', key: 'copy-all' },
  { label: '导出', key: 'export' },
  { label: '删除', key: 'delete' },
];

// 方法
const getNoteTypeLabel = (type: NoteType): string => {
  const labels: Record<NoteType, string> = {
    login: '登录凭据',
    ssh_key: 'SSH密钥',
    api_key: 'API密钥',
    certificate: '证书',
    database: '数据库',
    totp: 'TOTP',
    tech_note: '技术笔记',
  };
  return labels[type] || type;
};

const getNoteTypeColor = (type: NoteType): string => {
  const colors: Record<NoteType, string> = {
    login: '#18a058',
    ssh_key: '#2080f0',
    api_key: '#f0a020',
    certificate: '#d03050',
    database: '#7c3aed',
    totp: '#059669',
    tech_note: '#6b7280',
  };
  return colors[type] || '#6b7280';
};

const getNoteTypeTagType = (type: NoteType): string => {
  const types: Record<NoteType, string> = {
    login: 'success',
    ssh_key: 'info',
    api_key: 'warning',
    certificate: 'error',
    database: 'primary',
    totp: 'success',
    tech_note: 'default',
  };
  return types[type] || 'default';
};

const getNoteTypeIcon = (type: NoteType) => {
  return () => h('svg', { viewBox: '0 0 24 24' }, [
    h('path', { fill: 'currentColor', d: 'M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z' })
  ]);
};

const formatTime = (timeStr: string): string => {
  return new Date(timeStr).toLocaleString();
};

const openUrl = (url: string) => {
  chrome.tabs.create({ url });
};

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('已复制到剪贴板');
  } catch (error) {
    message.error('复制失败');
  }
};

const generateTotp = async () => {
  try {
    const response = await notesStore.generateTotp(props.note.id);
    if (response) {
      totpCode.value = {
        code: response.code,
        remaining: response.remaining_seconds,
        percentage: (response.remaining_seconds / 30) * 100,
      };
      startTotpCountdown();
    }
  } catch (error) {
    message.error('生成验证码失败');
  }
};

const getTotpColor = (remaining: number): string => {
  if (remaining <= 10) return '#d03050';
  if (remaining <= 20) return '#f0a020';
  return '#18a058';
};

const handleAction = (key: string) => {
  switch (key) {
    case 'copy-all':
      copyToClipboard(JSON.stringify(props.note, null, 2));
      break;
    case 'export':
      // TODO: 实现导出功能
      message.info('导出功能开发中...');
      break;
    case 'delete':
      emit('delete', props.note);
      break;
  }
};

// TOTP倒计时
let totpInterval: number | null = null;

const startTotpCountdown = () => {
  if (totpInterval) {
    clearInterval(totpInterval);
  }
  
  totpInterval = window.setInterval(() => {
    if (totpCode.value) {
      totpCode.value.remaining--;
      totpCode.value.percentage = (totpCode.value.remaining / 30) * 100;
      
      if (totpCode.value.remaining <= 0) {
        generateTotp(); // 自动刷新
      }
    }
  }, 1000);
};

// 生命周期
onMounted(() => {
  if (props.note.note_type === 'totp') {
    generateTotp();
  }
});

onUnmounted(() => {
  if (totpInterval) {
    clearInterval(totpInterval);
  }
});
</script>

<style scoped>
.note-detail {
  height: 100%;
  overflow-y: auto;
}

.note-header {
  flex: 1;
}

.note-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--n-text-color);
}

.note-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section h3,
.tags-section h3,
.content-section h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
}

.totp-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.totp-code .code {
  font-size: 18px;
  font-weight: 600;
  color: var(--n-primary-color);
  letter-spacing: 2px;
}

.totp-code .remaining {
  font-size: 12px;
  color: var(--n-text-color-2);
}

:deep(.n-card-header) {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--n-border-color);
}

:deep(.n-descriptions-item-label) {
  font-weight: 600;
}
</style>
