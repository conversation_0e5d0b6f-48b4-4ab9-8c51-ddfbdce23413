// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#a194fea0-d065-4b72-85be-8e349bb0f85e"
//   Timestamp: "2025-08-05T16:40:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "消息通信机制完整，支持popup、content、background通信"
// }}
// 扩展消息通信工具

import type { ExtensionMessage, MessageResponse, MessageType } from '../types';

// 重新导出类型
export { MessageType } from '../types';

// 生成唯一请求ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 发送消息到background script
export async function sendToBackground<T = any>(
  type: MessageType,
  payload?: any
): Promise<MessageResponse<T>> {
  const message: ExtensionMessage = {
    type,
    payload,
    requestId: generateRequestId(),
  };

  try {
    const response = await chrome.runtime.sendMessage(message);
    return response as MessageResponse<T>;
  } catch (error) {
    console.error('Failed to send message to background:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      requestId: message.requestId,
    };
  }
}

// 发送消息到content script
export async function sendToContentScript<T = any>(
  tabId: number,
  type: MessageType,
  payload?: any
): Promise<MessageResponse<T>> {
  const message: ExtensionMessage = {
    type,
    payload,
    requestId: generateRequestId(),
  };

  try {
    const response = await chrome.tabs.sendMessage(tabId, message);
    return response as MessageResponse<T>;
  } catch (error) {
    console.error('Failed to send message to content script:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      requestId: message.requestId,
    };
  }
}

// 发送消息到当前活动标签页
export async function sendToActiveTab<T = any>(
  type: MessageType,
  payload?: any
): Promise<MessageResponse<T>> {
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tabs.length === 0 || !tabs[0].id) {
      throw new Error('No active tab found');
    }

    return await sendToContentScript<T>(tabs[0].id, type, payload);
  } catch (error) {
    console.error('Failed to send message to active tab:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// 消息处理器类型
export type MessageHandler<T = any, R = any> = (
  payload: T,
  sender: chrome.runtime.MessageSender
) => Promise<MessageResponse<R>> | MessageResponse<R>;

// 消息路由器
export class MessageRouter {
  private handlers = new Map<MessageType, MessageHandler>();

  // 注册消息处理器
  register<T = any, R = any>(type: MessageType, handler: MessageHandler<T, R>): void {
    this.handlers.set(type, handler);
  }

  // 处理接收到的消息
  async handle(
    message: ExtensionMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    const handler = this.handlers.get(message.type);
    
    if (!handler) {
      return {
        success: false,
        error: `No handler registered for message type: ${message.type}`,
        requestId: message.requestId,
      };
    }

    try {
      const response = await handler(message.payload, sender);
      return {
        ...response,
        requestId: message.requestId,
      };
    } catch (error) {
      console.error(`Error handling message ${message.type}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        requestId: message.requestId,
      };
    }
  }

  // 启动消息监听器
  startListening(): void {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      // 验证消息格式
      if (!message || typeof message.type !== 'string') {
        sendResponse({
          success: false,
          error: 'Invalid message format',
        });
        return false;
      }

      // 异步处理消息
      this.handle(message as ExtensionMessage, sender)
        .then(sendResponse)
        .catch((error) => {
          console.error('Message handling failed:', error);
          sendResponse({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            requestId: message.requestId,
          });
        });

      return true; // 保持消息通道开放
    });
  }
}

// 创建全局消息路由器实例
export const messageRouter = new MessageRouter();

// 通知工具
export async function showNotification(
  title: string,
  message: string,
  type: 'basic' | 'image' | 'list' | 'progress' = 'basic'
): Promise<void> {
  try {
    await chrome.notifications.create({
      type,
      iconUrl: '/icon-48.png',
      title,
      message,
    });
  } catch (error) {
    console.error('Failed to show notification:', error);
  }
}

// 错误通知
export async function showErrorNotification(error: string): Promise<void> {
  await showNotification('SecureFox Error', error);
}

// 成功通知
export async function showSuccessNotification(message: string): Promise<void> {
  await showNotification('SecureFox', message);
}

// 获取当前活动标签页信息
export async function getActiveTab(): Promise<chrome.tabs.Tab | null> {
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    return tabs[0] || null;
  } catch (error) {
    console.error('Failed to get active tab:', error);
    return null;
  }
}

// 检查content script是否已注入
export async function isContentScriptInjected(tabId: number): Promise<boolean> {
  try {
    const response = await sendToContentScript(tabId, 'PING' as MessageType);
    return response.success;
  } catch {
    return false;
  }
}

// 确保content script已注入
export async function ensureContentScript(tabId: number): Promise<boolean> {
  try {
    const isInjected = await isContentScriptInjected(tabId);
    if (isInjected) {
      return true;
    }

    // 注入content script
    await chrome.scripting.executeScript({
      target: { tabId },
      files: ['content-scripts/main.js'],
    });

    // 等待一小段时间让脚本初始化
    await new Promise(resolve => setTimeout(resolve, 100));

    return await isContentScriptInjected(tabId);
  } catch (error) {
    console.error('Failed to ensure content script:', error);
    return false;
  }
}

// 批量发送消息
export async function broadcastToAllTabs<T = any>(
  type: MessageType,
  payload?: any
): Promise<MessageResponse<T>[]> {
  try {
    const tabs = await chrome.tabs.query({});
    const promises = tabs
      .filter(tab => tab.id !== undefined)
      .map(tab => sendToContentScript<T>(tab.id!, type, payload));

    return await Promise.all(promises);
  } catch (error) {
    console.error('Failed to broadcast to all tabs:', error);
    return [];
  }
}
