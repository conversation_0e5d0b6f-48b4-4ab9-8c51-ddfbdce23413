-- SecureFox 数据库初始化脚本
-- 创建用户表、统一笔记表、模板表等

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    salt TEXT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户邮箱索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- 统一安全笔记表
CREATE TABLE IF NOT EXISTS secure_notes (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    note_type TEXT NOT NULL CHECK (note_type IN ('login', 'ssh_key', 'api_key', 'certificate', 'database', 'totp', 'tech_note')),
    content TEXT NOT NULL, -- JSON格式存储
    tags TEXT NOT NULL DEFAULT '[]', -- JSON数组格式
    is_favorite BOOLEAN NOT NULL DEFAULT FALSE,
    template_id TEXT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES note_templates(id) ON DELETE SET NULL
);

-- 创建笔记相关索引
CREATE INDEX IF NOT EXISTS idx_secure_notes_user_id ON secure_notes(user_id);
CREATE INDEX IF NOT EXISTS idx_secure_notes_type ON secure_notes(note_type);
CREATE INDEX IF NOT EXISTS idx_secure_notes_favorite ON secure_notes(is_favorite);
CREATE INDEX IF NOT EXISTS idx_secure_notes_created_at ON secure_notes(created_at);

-- 笔记模板表
CREATE TABLE IF NOT EXISTS note_templates (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    content_template TEXT NOT NULL, -- JSON格式模板
    category TEXT NOT NULL, -- 模板分类
    is_system BOOLEAN NOT NULL DEFAULT FALSE, -- 是否为系统模板
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建模板索引
CREATE INDEX IF NOT EXISTS idx_note_templates_category ON note_templates(category);
CREATE INDEX IF NOT EXISTS idx_note_templates_system ON note_templates(is_system);

-- 插入系统默认模板
INSERT OR IGNORE INTO note_templates (id, name, description, content_template, category, is_system) VALUES
('tpl_login_basic', '基础登录', '网站登录凭据模板', '{"username":"","password":"","url":"","notes":""}', 'login', TRUE),
('tpl_ssh_key', 'SSH密钥', 'SSH密钥对模板', '{"private_key":"","public_key":"","passphrase":"","host":"","username":"","port":22}', 'ssh_key', TRUE),
('tpl_api_key', 'API密钥', 'API密钥和令牌模板', '{"api_key":"","secret":"","endpoint":"","description":"","headers":{}}', 'api_key', TRUE),
('tpl_certificate', 'SSL证书', 'SSL证书模板', '{"certificate":"","private_key":"","ca_chain":"","passphrase":"","domain":"","expires_at":""}', 'certificate', TRUE),
('tpl_database', '数据库连接', '数据库连接信息模板', '{"host":"","port":"","database":"","username":"","password":"","connection_string":"","type":""}', 'database', TRUE),
('tpl_totp', 'TOTP验证', 'TOTP两步验证模板', '{"secret":"","issuer":"","account":"","algorithm":"SHA1","digits":6,"period":30}', 'totp', TRUE),
('tpl_tech_note', '技术笔记', '技术笔记和配置模板', '{"content":"","language":"","tags":[],"references":[]}', 'tech_note', TRUE);

-- 创建更新时间触发器
CREATE TRIGGER IF NOT EXISTS update_users_updated_at 
    AFTER UPDATE ON users
    FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_secure_notes_updated_at 
    AFTER UPDATE ON secure_notes
    FOR EACH ROW
BEGIN
    UPDATE secure_notes SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_note_templates_updated_at 
    AFTER UPDATE ON note_templates
    FOR EACH ROW
BEGIN
    UPDATE note_templates SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
