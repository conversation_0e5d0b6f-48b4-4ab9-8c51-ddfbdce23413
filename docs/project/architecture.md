# 安全狐密码管理器 - 架构设计文档
**定位**: 面向个人开发者/技术人员的密钥和凭据管理工具

## 系统架构概览

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    浏览器扩展 (WXT)                          │
├─────────────────┬─────────────────┬─────────────────────────┤
│   密码自动填充   │   笔记快速查看   │   条目搜索 & 生成器      │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
                    ┌─────────────────┐
                    │   Web应用UI     │
                    │   (可选)        │
                    └─────────────────┘
                              │
          ┌───────────────────────────────────────────────────┐
          │              后端API (Rust Axum)                  │
          ├─────────────┬─────────────┬─────────────┬─────────┤
          │  用户认证    │  保险库API   │   搜索API   │ 同步API │
          └─────────────┴─────────────┴─────────────┴─────────┘
                              │
          ┌───────────────────────────────────────────────────┐
          │                数据层 (SQLite)                    │
          ├─────────────┬─────────────┬─────────────────────────┤
          │   用户表     │  统一条目表  │      搜索索引表        │
          └─────────────┴─────────────┴─────────────────────────┘
```

## 核心模块设计

### 1. 用户认证模块
**职责**: 用户注册、登录、会话管理、密钥派生

**核心组件**:
- 主密码验证器
- PBKDF2密钥派生服务
- JWT会话管理
- 密钥内存管理

**API接口**:
```rust
POST /api/auth/register    // 用户注册
POST /api/auth/login       // 用户登录  
POST /api/auth/logout      // 用户登出
GET  /api/auth/verify      // 会话验证
```

### 2. 加密服务模块
**职责**: 客户端数据加解密、密钥管理

**核心组件**:
- AES-256-GCM加密器
- 安全随机数生成器
- 密钥派生函数
- 内存安全清理

**加密流程**:
1. 主密码 → PBKDF2-SHA256 → 主密钥
2. 主密钥 + 随机盐 → 条目加密密钥
3. 条目数据 → AES-256-GCM → 加密数据

### 3. 统一笔记管理模块 (架构简化)
**职责**: 统一管理所有类型的安全笔记条目

**核心设计理念**: 将所有类型（SSH密钥、API密钥、技术笔记等）统一为不同类型的安全笔记

**支持的笔记类型** (面向技术用户):
```rust
enum NoteType {
    Login,        // 网站登录凭据
    SshKey,       // SSH密钥对
    ApiKey,       // API密钥和令牌
    Certificate,  // SSL证书、代码签名证书
    Database,     // 数据库连接信息
    Totp,         // TOTP两步验证
    TechNote,     // 技术笔记（配置、命令等）
}
```

**统一数据模型**:
```rust
struct SecureNote {
    id: String,
    user_id: String,
    title: String,
    note_type: NoteType,
    content: serde_json::Value,  // 灵活的JSON内容存储
    tags: Vec<String>,
    is_favorite: bool,
    template_id: Option<String>,
    created_at: DateTime,
    updated_at: DateTime,
}
```

**内容结构示例**:
```rust
// Login类型内容
{
  "url": "https://github.com",
  "username": "<EMAIL>",
  "password": "encrypted_password",
  "notes": "Additional notes"
}

// SshKey类型内容
{
  "key_type": "ed25519",
  "private_key": "encrypted_private_key",
  "public_key": "ssh-ed25519 AAAAC3...",
  "passphrase": "encrypted_passphrase",
  "servers": ["server1.com", "server2.com"]
}

// ApiKey类型内容
{
  "service": "GitHub",
  "api_key": "encrypted_token",
  "permissions": ["repo", "user"],
  "expires_at": "2024-12-31T23:59:59Z"
}
```

**统一API接口**:
```rust
GET    /api/notes                 // 获取所有笔记
POST   /api/notes                 // 创建新笔记
GET    /api/notes/{id}            // 获取特定笔记
PUT    /api/notes/{id}            // 更新笔记
DELETE /api/notes/{id}            // 删除笔记
GET    /api/notes/search?q={}     // 搜索笔记
GET    /api/notes/types/{type}    // 按类型获取笔记
GET    /api/totp/{id}/generate    // 生成TOTP验证码（特殊接口）
```

### 4. 浏览器扩展模块 (WXT)
**职责**: 浏览器集成、用户界面、自动化功能

**核心功能** (技术用户导向):
- **密码自动填充**: 检测登录表单，自动填充凭据
- **SSH密钥集成**: 与SSH Agent集成，自动使用SSH密钥
- **API密钥快速复制**: 一键复制API密钥到剪贴板
- **TOTP生成器**: 实时生成两步验证码
- **技术笔记查看**: 快速查看配置和命令
- **条目搜索**: 按类型和标签搜索技术资源

**统一UI组件** (基于Naive UI):
```
popup/
├── login.vue              // 登录界面
├── vault.vue              // 笔记库主界面
├── note-list.vue          // 笔记列表（按类型分组）
├── note-editor.vue        // 统一笔记编辑器
├── content-editors/       // 内容编辑器组件
│   ├── login-editor.vue   // 登录凭据编辑器
│   ├── ssh-key-editor.vue // SSH密钥编辑器
│   ├── api-key-editor.vue // API密钥编辑器
│   ├── cert-editor.vue    // 证书编辑器
│   ├── db-editor.vue      // 数据库编辑器
│   ├── totp-editor.vue    // TOTP编辑器
│   └── tech-note-editor.vue // 技术笔记编辑器
├── totp-generator.vue     // TOTP生成器界面
├── search-filter.vue      // 搜索和过滤
└── password-gen.vue       // 密码/密钥生成器
```

### 5. 笔记编辑器模块 (重大升级)
**职责**: 高级安全笔记的创建、编辑、管理

**核心功能**:
- **富文本编辑系统**:
  - Markdown完整支持（标题、列表、粗体、斜体、代码块、表格）
  - 实时预览模式（编辑和预览并排显示）
  - 语法高亮（代码块的语法高亮显示）
  - Emoji支持（标题和内容中的表情符号）

- **高级搜索与组织**:
  - 全文搜索引擎（搜索标题、内容、标签）
  - 多级标签系统（支持嵌套标签和标签层次）
  - 智能过滤（按日期、标签、收藏状态过滤）
  - 正则表达式搜索（高级用户的强大搜索）

- **模板系统**:
  - 预定义模板（旅行、医疗、金融、技术、许可证等）
  - 自定义模板创建和编辑
  - 模板共享功能

- **版本历史系统**:
  - 自动版本保存（每次编辑自动创建版本）
  - 版本对比视图（可视化显示版本差异）
  - 一键回滚（快速恢复到历史版本）

- **文件附件系统**:
  - 多文件附件支持（每个笔记支持多个附件）
  - 文件预览（图片、PDF等文件的预览功能）
  - 文件类型管理（可配置的文件类型限制）
  - 加密存储（附件同样采用零知识加密）

- **用户体验优化**:
  - 收藏夹系统（快速访问重要笔记）
  - 最近使用（智能显示最近编辑的笔记）
  - 拖拽排序（自定义笔记排序）
  - 快捷键支持（提高编辑效率）

**升级后的数据结构**:
```rust
struct SecureNote {
    id: String,
    title: String,
    content: String,           // Markdown格式内容
    content_type: ContentType, // markdown, plain_text
    tags: Vec<String>,         // 多级标签支持
    template_id: Option<String>,
    attachments: Vec<Attachment>,
    version_history: Vec<NoteVersion>,
    is_favorite: bool,
    view_count: u32,
    last_viewed_at: Option<DateTime>,
    created_at: DateTime,
    updated_at: DateTime,
}

struct NoteTemplate {
    id: String,
    name: String,
    description: String,
    content_template: String,  // Markdown模板内容
    category: String,          // 模板分类
    is_system: bool,          // 系统模板 vs 用户模板
    usage_count: u32,
    created_at: DateTime,
}

struct Attachment {
    id: String,
    note_id: String,
    filename: String,
    content_type: String,
    size: u64,
    encrypted_data: Vec<u8>,
    thumbnail: Option<Vec<u8>>, // 图片缩略图
    created_at: DateTime,
}

struct NoteVersion {
    id: String,
    note_id: String,
    version_number: u32,
    title: String,
    content: String,
    change_summary: String,    // 变更摘要
    created_at: DateTime,
}

enum ContentType {
    Markdown,
    PlainText,
}
```

### 6. 密码生成器模块
**职责**: 强密码生成、密码强度评估

**核心功能**:
- 可配置密码生成（长度、字符集）
- 密码强度评估算法
- 生成历史记录
- 自定义生成规则

## 数据库设计

### 表结构设计
```sql
-- 用户表
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,  -- 主密码的哈希
    salt TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 统一条目表
CREATE TABLE vault_items (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    item_type TEXT NOT NULL,      -- 'login', 'note', 'card', 'identity'
    name TEXT NOT NULL,
    encrypted_data TEXT NOT NULL, -- 加密的JSON数据
    tags TEXT,                    -- JSON数组格式的标签
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 笔记模板表
CREATE TABLE note_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    content_template TEXT NOT NULL,
    category TEXT NOT NULL,
    is_system BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 笔记版本历史表
CREATE TABLE note_versions (
    id TEXT PRIMARY KEY,
    note_id TEXT NOT NULL,
    version_number INTEGER NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    change_summary TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (note_id) REFERENCES vault_items(id)
);

-- 文件附件表
CREATE TABLE attachments (
    id TEXT PRIMARY KEY,
    item_id TEXT NOT NULL,
    filename TEXT NOT NULL,
    content_type TEXT NOT NULL,
    size INTEGER NOT NULL,
    encrypted_data BLOB NOT NULL,
    thumbnail BLOB,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES vault_items(id)
);

-- 搜索索引表 (用于全文搜索)
CREATE TABLE search_index (
    item_id TEXT NOT NULL,
    content TEXT NOT NULL,        -- 解密后的可搜索内容
    content_type TEXT NOT NULL,   -- 'title', 'content', 'tags'
    FOREIGN KEY (item_id) REFERENCES vault_items(id)
);
```

## 安全设计

### 零知识架构实现
1. **客户端加密**: 所有敏感数据在客户端加密后传输
2. **服务端盲存**: 服务端只存储加密数据，无法解密
3. **密钥隔离**: 加密密钥只存在于客户端内存中
4. **会话安全**: JWT token不包含敏感信息

### 加密实现细节
```rust
// 密钥派生
fn derive_key(master_password: &str, salt: &[u8]) -> [u8; 32] {
    pbkdf2::pbkdf2::<Hmac<Sha256>>(
        master_password.as_bytes(),
        salt,
        100_000, // 迭代次数
        &mut key
    );
}

// 数据加密
fn encrypt_data(data: &str, key: &[u8]) -> Result<String> {
    let cipher = Aes256Gcm::new(key.into());
    let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
    let ciphertext = cipher.encrypt(&nonce, data.as_bytes())?;
    
    // 返回 nonce + ciphertext 的base64编码
    Ok(base64::encode([nonce.as_slice(), &ciphertext].concat()))
}
```

## 技术栈详细说明

### 后端技术栈 (Rust)
- **Web框架**: Axum (异步、类型安全)
- **数据库**: SQLite + sqlx (类型安全的SQL)
- **加密库**: ring/rustcrypto (AES-256-GCM, PBKDF2)
- **认证**: jsonwebtoken (JWT)
- **序列化**: serde (JSON处理)

### 前端技术栈 (WXT)
- **框架**: WXT + Vue 3 + TypeScript
- **UI库**: Naive UI (统一选择)
- **状态管理**: Pinia
- **加密**: WebCrypto API
- **构建工具**: Vite
- **代码编辑器**: Monaco Editor (用于代码类型笔记)

## 开发优先级

### Phase 1: 统一笔记系统 (3-4周，简化1周)
1. ✅ 用户认证系统（主密码 + 零知识加密）
2. ✅ 统一笔记管理系统（支持7种笔记类型）
3. ✅ Naive UI界面实现（统一的编辑器和查看器）
4. ✅ 浏览器扩展集成（自动填充和密码捕获）
5. ✅ TOTP生成器集成（作为笔记类型之一）
6. ✅ 基础搜索和过滤功能

### Phase 2: 扩展功能 (2-3周，简化1周)
1. ✅ 高级搜索和标签管理
2. ✅ 模板系统（针对不同笔记类型）
3. ✅ 密码/密钥生成器
4. ✅ 导入导出功能
5. ✅ CLI工具（可选，命令行访问）
6. ✅ SSH Agent集成（可选）

### Phase 3: 高级功能 (按需)
1. 云端同步（多设备同步）
2. 团队共享（技术团队的密钥共享）
3. 审计日志（访问记录和安全审计）
4. SSH Agent高级集成
5. 证书到期监控和提醒

## 性能和扩展性考虑

### 性能优化
- **懒加载**: 条目按需加载和解密
- **搜索优化**: 建立搜索索引，支持模糊搜索
- **内存管理**: 及时清理敏感数据内存
- **缓存策略**: 合理的客户端缓存机制

### 扩展性设计
- **插件架构**: 支持第三方扩展
- **API版本控制**: 向后兼容的API设计
- **多租户支持**: 为企业版本预留架构空间
- **国际化**: 多语言支持框架

这个架构设计充分考虑了笔记功能的需求，同时保持了系统的安全性、可扩展性和用户体验。
