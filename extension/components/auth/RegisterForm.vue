<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#b290202c-b04c-4580-9b7a-7c86848ed54f"
  Timestamp: "2025-08-05T17:00:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "注册表单组件完整，支持密码强度指示和协议确认"
}} -->
<template>
  <div class="register-form">
    <div class="form-header">
      <n-icon size="48" color="#18a058">
        <svg viewBox="0 0 24 24">
          <path fill="currentColor" d="M15,14C12.33,14 7,15.33 7,18V20H23V18C23,15.33 17.67,14 15,14M6,10V7H4V10H1V12H4V15H6V12H9V10M15,12A4,4 0 0,0 19,8A4,4 0 0,0 15,4A4,4 0 0,0 11,8A4,4 0 0,0 15,12Z" />
        </svg>
      </n-icon>
      <h2>创建 SecureFox 账户</h2>
      <p>设置您的主密码来保护您的数据</p>
    </div>

    <n-form 
      ref="formRef" 
      :model="formData" 
      :rules="formRules"
      size="large"
      @keyup.enter="handleSubmit"
    >
      <n-form-item path="email" label="邮箱地址">
        <n-input
          v-model:value="formData.email"
          type="email"
          placeholder="请输入您的邮箱地址"
          :disabled="isLoading"
          clearable
          @focus="clearError"
        >
          <template #prefix>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z" />
              </svg>
            </n-icon>
          </template>
        </n-input>
      </n-form-item>

      <n-form-item path="master_password" label="主密码">
        <n-input
          v-model:value="formData.master_password"
          :type="showPassword ? 'text' : 'password'"
          placeholder="请设置您的主密码"
          :disabled="isLoading"
          show-password-on="click"
          @input="updatePasswordStrength"
          @focus="clearError"
        >
          <template #prefix>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
              </svg>
            </n-icon>
          </template>
        </n-input>
        
        <!-- 密码强度指示器 -->
        <div v-if="formData.master_password" class="password-strength">
          <div class="strength-bar">
            <div 
              class="strength-fill" 
              :class="passwordStrength.level"
              :style="{ width: passwordStrength.percentage + '%' }"
            ></div>
          </div>
          <div class="strength-text">
            <span :class="passwordStrength.level">
              {{ passwordStrength.text }}
            </span>
            <n-popover trigger="hover" placement="right">
              <template #trigger>
                <n-icon size="14" style="margin-left: 4px; cursor: help;">
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z" />
                  </svg>
                </n-icon>
              </template>
              <div class="password-requirements">
                <div>密码要求：</div>
                <div :class="{ 'requirement-met': passwordChecks.length }">
                  • 至少8个字符
                </div>
                <div :class="{ 'requirement-met': passwordChecks.uppercase }">
                  • 包含大写字母
                </div>
                <div :class="{ 'requirement-met': passwordChecks.lowercase }">
                  • 包含小写字母
                </div>
                <div :class="{ 'requirement-met': passwordChecks.number }">
                  • 包含数字
                </div>
                <div :class="{ 'requirement-met': passwordChecks.special }">
                  • 包含特殊字符
                </div>
              </div>
            </n-popover>
          </div>
        </div>
      </n-form-item>

      <n-form-item path="confirmPassword" label="确认密码">
        <n-input
          v-model:value="formData.confirmPassword"
          :type="showConfirmPassword ? 'text' : 'password'"
          placeholder="请再次输入密码"
          :disabled="isLoading"
          show-password-on="click"
          @focus="clearError"
        >
          <template #prefix>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z" />
              </svg>
            </n-icon>
          </template>
        </n-input>
      </n-form-item>

      <!-- 服务条款和隐私政策 -->
      <n-form-item path="agreeToTerms">
        <n-checkbox 
          v-model:checked="formData.agreeToTerms"
          :disabled="isLoading"
        >
          <span class="terms-text">
            我已阅读并同意
            <n-button text type="primary" size="small" @click="showTerms">
              服务条款
            </n-button>
            和
            <n-button text type="primary" size="small" @click="showPrivacy">
              隐私政策
            </n-button>
          </span>
        </n-checkbox>
      </n-form-item>

      <!-- 错误提示 -->
      <n-alert 
        v-if="error" 
        type="error" 
        :title="error"
        closable
        @close="clearError"
        style="margin-bottom: 16px"
      />

      <!-- 注册按钮 -->
      <n-form-item>
        <n-button
          type="primary"
          size="large"
          block
          :loading="isLoading"
          :disabled="!canSubmit"
          @click="handleSubmit"
        >
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M15,14C12.33,14 7,15.33 7,18V20H23V18C23,15.33 17.67,14 15,14M6,10V7H4V10H1V12H4V15H6V12H9V10M15,12A4,4 0 0,0 19,8A4,4 0 0,0 15,4A4,4 0 0,0 11,8A4,4 0 0,0 15,12Z" />
              </svg>
            </n-icon>
          </template>
          创建账户
        </n-button>
      </n-form-item>

      <!-- 切换到登录 -->
      <n-form-item>
        <n-space justify="center" style="width: 100%">
          <span class="switch-text">已有账号？</span>
          <n-button 
            text 
            type="primary"
            @click="$emit('switch-to-login')"
            :disabled="isLoading"
          >
            立即登录
          </n-button>
        </n-space>
      </n-form-item>
    </n-form>

    <!-- 安全提示 -->
    <div class="security-notice">
      <n-alert type="info" :show-icon="false">
        <template #header>
          <n-icon size="16">
            <svg viewBox="0 0 24 24">
              <path fill="currentColor" d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.11,7 14,7.89 14,9C14,10.11 13.11,11 12,11C10.89,11 10,10.11 10,9C10,7.89 10.89,7 12,7M12,14.3C13.07,14.3 14.8,14.5 15.6,15.6C14.5,16.9 13.3,17.5 12,17.5C10.7,17.5 9.5,16.9 8.4,15.6C9.2,14.5 10.93,14.3 12,14.3Z" />
            </svg>
          </n-icon>
          安全提示
        </template>
        您的主密码将用于加密所有数据。请确保使用强密码并妥善保管，我们无法帮您找回丢失的主密码。
      </n-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NIcon,
  NCheckbox,
  NSpace,
  NAlert,
  NPopover,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui';
import type { RegisterRequest } from '../../types';

// Props & Emits
interface Props {
  loading?: boolean;
  error?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
});

const emit = defineEmits<{
  submit: [data: RegisterRequest];
  'switch-to-login': [];
  'clear-error': [];
  'show-terms': [];
  'show-privacy': [];
}>();

// 响应式数据
const formRef = ref<FormInst | null>(null);
const showPassword = ref(false);
const showConfirmPassword = ref(false);

const formData = reactive<RegisterRequest & { confirmPassword: string; agreeToTerms: boolean }>({
  email: '',
  master_password: '',
  confirmPassword: '',
  agreeToTerms: false,
});

// 密码强度检查
const passwordChecks = computed(() => ({
  length: formData.master_password.length >= 8,
  uppercase: /[A-Z]/.test(formData.master_password),
  lowercase: /[a-z]/.test(formData.master_password),
  number: /[0-9]/.test(formData.master_password),
  special: /[!@#$%^&*(),.?":{}|<>]/.test(formData.master_password),
}));

const passwordStrength = computed(() => {
  const checks = passwordChecks.value;
  const score = Object.values(checks).filter(Boolean).length;
  
  if (score === 0) return { level: 'weak', text: '密码强度：很弱', percentage: 0 };
  if (score <= 2) return { level: 'weak', text: '密码强度：弱', percentage: 25 };
  if (score <= 3) return { level: 'medium', text: '密码强度：中等', percentage: 50 };
  if (score <= 4) return { level: 'strong', text: '密码强度：强', percentage: 75 };
  return { level: 'very-strong', text: '密码强度：很强', percentage: 100 };
});

// 计算属性
const isLoading = computed(() => props.loading);
const error = computed(() => props.error);

const canSubmit = computed(() => {
  return formData.agreeToTerms && 
         passwordStrength.value.level !== 'weak' &&
         formData.master_password === formData.confirmPassword;
});

// 表单验证规则
const formRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' },
  ],
  master_password: [
    { required: true, message: '请设置主密码', trigger: 'blur' },
    { min: 8, message: '主密码长度至少8位', trigger: 'blur' },
    {
      validator: (rule, value) => {
        const checks = passwordChecks.value;
        if (!checks.uppercase) return new Error('密码必须包含大写字母');
        if (!checks.lowercase) return new Error('密码必须包含小写字母');
        if (!checks.number) return new Error('密码必须包含数字');
        if (!checks.special) return new Error('密码必须包含特殊字符');
        return true;
      },
      trigger: 'blur',
    },
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        return value === formData.master_password ? true : new Error('两次输入的密码不一致');
      },
      trigger: 'blur',
    },
  ],
  agreeToTerms: [
    {
      validator: (rule, value) => {
        return value ? true : new Error('请同意服务条款和隐私政策');
      },
      trigger: 'change',
    },
  ],
};

// 方法
const clearError = () => {
  emit('clear-error');
};

const updatePasswordStrength = () => {
  // 密码强度会自动通过计算属性更新
};

const showTerms = () => {
  emit('show-terms');
};

const showPrivacy = () => {
  emit('show-privacy');
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    const { confirmPassword, agreeToTerms, ...registerData } = formData;
    emit('submit', registerData);
  } catch (error) {
    console.error('Form validation failed:', error);
  }
};

// 生命周期
onMounted(() => {
  // 可以添加一些初始化逻辑
});
</script>

<style scoped>
.register-form {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: 24px;
}

.form-header h2 {
  margin: 16px 0 8px 0;
  color: var(--n-text-color);
  font-size: 24px;
  font-weight: 600;
}

.form-header p {
  margin: 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.password-strength {
  margin-top: 8px;
}

.strength-bar {
  height: 4px;
  background-color: var(--n-border-color);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.strength-fill {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
}

.strength-fill.weak { background-color: #e74c3c; }
.strength-fill.medium { background-color: #f39c12; }
.strength-fill.strong { background-color: #3498db; }
.strength-fill.very-strong { background-color: #27ae60; }

.strength-text {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.strength-text .weak { color: #e74c3c; }
.strength-text .medium { color: #f39c12; }
.strength-text .strong { color: #3498db; }
.strength-text .very-strong { color: #27ae60; }

.password-requirements {
  font-size: 12px;
  line-height: 1.5;
}

.requirement-met {
  color: #27ae60;
}

.terms-text {
  font-size: 14px;
  line-height: 1.5;
}

.switch-text {
  color: var(--n-text-color-2);
  font-size: 14px;
}

.security-notice {
  margin-top: 16px;
}

:deep(.n-input) {
  --n-height: 40px;
}

:deep(.n-button) {
  --n-height: 40px;
}
</style>
