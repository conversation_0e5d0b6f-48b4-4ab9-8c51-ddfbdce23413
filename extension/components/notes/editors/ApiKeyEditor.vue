<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "API密钥编辑器组件基础实现"
}} -->
<template>
  <div class="api-key-editor">
    <n-space vertical size="medium">
      <n-form-item label="服务名称" path="service">
        <n-input v-model:value="localValue.service" :disabled="disabled" />
      </n-form-item>
      <n-form-item label="API密钥" path="api_key">
        <n-input v-model:value="localValue.api_key" type="password" :disabled="disabled" />
      </n-form-item>
      <n-form-item label="权限" path="permissions">
        <n-dynamic-input v-model:value="localValue.permissions" :disabled="disabled" />
      </n-form-item>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { NSpace, NFormItem, NInput, NDynamicInput } from 'naive-ui';
import type { ApiKeyContent } from '../../../types';

interface Props {
  value: ApiKeyContent;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), { disabled: false });
const emit = defineEmits<{ 'update:value': [value: ApiKeyContent] }>();
const localValue = ref<ApiKeyContent>({ ...props.value });

watch(localValue, (newValue) => emit('update:value', { ...newValue }), { deep: true });
watch(() => props.value, (newValue) => { localValue.value = { ...newValue }; }, { deep: true });
</script>

<style scoped>
.api-key-editor { width: 100%; }
</style>
