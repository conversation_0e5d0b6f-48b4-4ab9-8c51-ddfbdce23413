<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#b290202c-b04c-4580-9b7a-7c86848ed54f"
  Timestamp: "2025-08-05T17:00:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "登录表单组件完整，支持密码可见性切换和键盘快捷键"
}} -->
<template>
  <div class="login-form">
    <div class="form-header">
      <n-icon size="48" color="#18a058">
        <svg viewBox="0 0 24 24">
          <path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
        </svg>
      </n-icon>
      <h2>登录到 SecureFox</h2>
      <p>使用您的主密码访问您的安全数据</p>
    </div>

    <n-form 
      ref="formRef" 
      :model="formData" 
      :rules="formRules"
      size="large"
      @keyup.enter="handleSubmit"
    >
      <n-form-item path="email" label="邮箱地址">
        <n-input
          v-model:value="formData.email"
          type="email"
          placeholder="请输入您的邮箱地址"
          :disabled="isLoading"
          clearable
          @focus="clearError"
        >
          <template #prefix>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z" />
              </svg>
            </n-icon>
          </template>
        </n-input>
      </n-form-item>

      <n-form-item path="master_password" label="主密码">
        <n-input
          v-model:value="formData.master_password"
          :type="showPassword ? 'text' : 'password'"
          placeholder="请输入您的主密码"
          :disabled="isLoading"
          show-password-on="click"
          @focus="clearError"
        >
          <template #prefix>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
              </svg>
            </n-icon>
          </template>
          <template #suffix>
            <n-button
              text
              @click="togglePasswordVisibility"
              :disabled="isLoading"
            >
              <n-icon>
                <svg v-if="showPassword" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z" />
                </svg>
                <svg v-else viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z" />
                </svg>
              </n-icon>
            </n-button>
          </template>
        </n-input>
      </n-form-item>

      <!-- 记住登录状态 -->
      <n-form-item>
        <n-space justify="space-between" style="width: 100%">
          <n-checkbox 
            v-model:checked="rememberMe"
            :disabled="isLoading"
          >
            记住登录状态
          </n-checkbox>
          <n-button 
            text 
            size="small"
            @click="$emit('forgot-password')"
            :disabled="isLoading"
          >
            忘记密码？
          </n-button>
        </n-space>
      </n-form-item>

      <!-- 错误提示 -->
      <n-alert 
        v-if="error" 
        type="error" 
        :title="error"
        closable
        @close="clearError"
        style="margin-bottom: 16px"
      />

      <!-- 登录按钮 -->
      <n-form-item>
        <n-button
          type="primary"
          size="large"
          block
          :loading="isLoading"
          @click="handleSubmit"
          :keyboard="false"
        >
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M10,17V14H3V10H10V7L15,12L10,17M10,2H19A2,2 0 0,1 21,4V20A2,2 0 0,1 19,22H10A2,2 0 0,1 8,20V18H10V20H19V4H10V6H8V4A2,2 0 0,1 10,2Z" />
              </svg>
            </n-icon>
          </template>
          登录 (Enter)
        </n-button>
      </n-form-item>

      <!-- 切换到注册 -->
      <n-form-item>
        <n-space justify="center" style="width: 100%">
          <span class="switch-text">还没有账号？</span>
          <n-button 
            text 
            type="primary"
            @click="$emit('switch-to-register')"
            :disabled="isLoading"
          >
            立即注册
          </n-button>
        </n-space>
      </n-form-item>
    </n-form>

    <!-- 快捷键提示 -->
    <div class="keyboard-hints">
      <n-text depth="3" style="font-size: 12px">
        快捷键：Enter 登录 • Ctrl+Shift+L 快速登录
      </n-text>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NIcon,
  NCheckbox,
  NSpace,
  NAlert,
  NText,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui';
import type { LoginRequest } from '../../types';

// Props & Emits
interface Props {
  loading?: boolean;
  error?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
});

const emit = defineEmits<{
  submit: [data: LoginRequest & { rememberMe: boolean }];
  'switch-to-register': [];
  'forgot-password': [];
  'clear-error': [];
}>();

// 响应式数据
const formRef = ref<FormInst | null>(null);
const showPassword = ref(false);
const rememberMe = ref(false);

const formData = reactive<LoginRequest>({
  email: '',
  master_password: '',
});

// 计算属性
const isLoading = computed(() => props.loading);
const error = computed(() => props.error);

// 表单验证规则
const formRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' },
  ],
  master_password: [
    { required: true, message: '请输入主密码', trigger: 'blur' },
    { min: 8, message: '主密码长度至少8位', trigger: 'blur' },
  ],
};

// 方法
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

const clearError = () => {
  emit('clear-error');
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    emit('submit', {
      ...formData,
      rememberMe: rememberMe.value,
    });
  } catch (error) {
    console.error('Form validation failed:', error);
  }
};

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl+Shift+L 快速登录
  if (event.ctrlKey && event.shiftKey && event.key === 'L') {
    event.preventDefault();
    handleSubmit();
  }
};

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
  
  // 从存储中恢复记住的邮箱
  chrome.storage.local.get(['rememberedEmail']).then((result) => {
    if (result.rememberedEmail) {
      formData.email = result.rememberedEmail;
      rememberMe.value = true;
    }
  });
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
.login-form {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: 24px;
}

.form-header h2 {
  margin: 16px 0 8px 0;
  color: var(--n-text-color);
  font-size: 24px;
  font-weight: 600;
}

.form-header p {
  margin: 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.switch-text {
  color: var(--n-text-color-2);
  font-size: 14px;
}

.keyboard-hints {
  text-align: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--n-border-color);
}

:deep(.n-input) {
  --n-height: 40px;
}

:deep(.n-button) {
  --n-height: 40px;
}
</style>
