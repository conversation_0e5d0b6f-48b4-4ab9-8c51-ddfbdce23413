#!/bin/bash

# SecureFox 开发环境启动脚本

set -e

echo "🚀 Starting SecureFox development environment..."

# 检查依赖
check_dependencies() {
    echo "📋 Checking dependencies..."
    
    if ! command -v cargo &> /dev/null; then
        echo "❌ Rust/Cargo not found. Please install Rust: https://rustup.rs/"
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js not found. Please install Node.js 18+: https://nodejs.org/"
        exit 1
    fi
    
    echo "✅ Dependencies check passed"
}

# 安装依赖
install_dependencies() {
    echo "📦 Installing dependencies..."
    
    # 安装前端依赖
    if [ ! -d "extension/node_modules" ]; then
        echo "Installing extension dependencies..."
        cd extension
        npm install
        cd ..
    fi
    
    echo "✅ Dependencies installed"
}

# 启动开发服务器
start_dev_servers() {
    echo "🔧 Starting development servers..."
    
    # 启动后端服务器
    echo "Starting backend server..."
    cd backend
    cargo run &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端启动
    sleep 3
    
    # 启动扩展开发服务器
    echo "Starting extension development server..."
    cd extension
    npm run dev &
    EXTENSION_PID=$!
    cd ..
    
    echo "✅ Development servers started"
    echo "📍 Backend: http://localhost:3000"
    echo "📍 Extension: Load unpacked extension from extension/.output/chrome-mv3"
    
    # 等待用户中断
    echo "Press Ctrl+C to stop all servers..."
    wait
}

# 清理函数
cleanup() {
    echo "🧹 Cleaning up..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$EXTENSION_PID" ]; then
        kill $EXTENSION_PID 2>/dev/null || true
    fi
    echo "✅ Cleanup completed"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 主流程
main() {
    check_dependencies
    install_dependencies
    start_dev_servers
}

main "$@"
