// 数据库测试程序
use securefox_backend::database::{DatabaseManager, CreateUserRequest, CreateNoteRequest};
use securefox_backend::database::repository::{UserRepository, NoteRepository, TemplateRepository};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    println!("🔧 测试数据库设计与初始化...");

    // 创建数据库管理器（使用内存数据库）
    let db_manager = DatabaseManager::new("sqlite::memory:").await?;
    println!("✅ 数据库连接成功");

    // 测试数据库健康检查
    db_manager.health_check().await?;
    println!("✅ 数据库健康检查通过");

    // 创建仓库实例
    let user_repo = db_manager.user_repository();
    let note_repo = db_manager.note_repository();

    // 测试用户创建
    let create_user_req = CreateUserRequest {
        email: "<EMAIL>".to_string(),
        password_hash: "hashed_password_123".to_string(),
        salt: "random_salt_456".to_string(),
    };

    let user = user_repo.create_user(create_user_req).await?;
    println!("✅ 用户创建成功: {}", user.email);

    // 测试笔记创建
    let create_note_req = CreateNoteRequest {
        user_id: user.id.clone(),
        title: "测试登录凭据".to_string(),
        note_type: "login".to_string(),
        content: r#"{"username":"testuser","password":"testpass","url":"https://example.com"}"#.to_string(),
        tags: r#"["test","login"]"#.to_string(),
        is_favorite: Some(true),
        template_id: Some("tpl_login_basic".to_string()),
    };

    let note = note_repo.create_note(create_note_req).await?;
    println!("✅ 笔记创建成功: {}", note.title);

    // 测试查询用户笔记
    let user_notes = note_repo.find_notes_by_user(&user.id).await?;
    println!("✅ 查询用户笔记成功，共 {} 条", user_notes.len());

    // 测试按类型查询笔记
    let login_notes = note_repo.find_notes_by_type(&user.id, "login").await?;
    println!("✅ 查询登录类型笔记成功，共 {} 条", login_notes.len());

    // 测试模板查询
    let template_repo = db_manager.template_repository();
    let templates = template_repo.find_all_templates().await?;
    println!("✅ 查询模板成功，共 {} 个模板", templates.len());

    println!("🎉 所有数据库测试通过！");

    Ok(())
}
