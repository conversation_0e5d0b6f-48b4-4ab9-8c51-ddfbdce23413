<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "笔记项组件完整，支持快速操作和状态显示"
}} -->
<template>
  <div 
    class="note-item"
    :class="{ 'note-item--favorite': note.is_favorite }"
    @click="$emit('click', note)"
  >
    <div class="note-content">
      <!-- 笔记图标和类型 -->
      <div class="note-icon">
        <n-icon :size="20" :color="getNoteTypeColor(note.note_type)">
          <component :is="getNoteTypeIcon(note.note_type)" />
        </n-icon>
      </div>

      <!-- 笔记信息 -->
      <div class="note-info">
        <div class="note-header">
          <h4 class="note-title">{{ note.title }}</h4>
          <div class="note-meta">
            <n-tag :type="getNoteTypeTagType(note.note_type)" size="tiny">
              {{ getNoteTypeLabel(note.note_type) }}
            </n-tag>
            <span class="note-time">{{ formatTime(note.updated_at) }}</span>
          </div>
        </div>

        <!-- 笔记预览 -->
        <div class="note-preview">
          <p>{{ getNotePreview(note) }}</p>
        </div>

        <!-- 标签 -->
        <div v-if="note.tags.length > 0" class="note-tags">
          <n-tag
            v-for="tag in note.tags.slice(0, 3)"
            :key="tag"
            size="small"
            type="default"
            class="note-tag"
          >
            {{ tag }}
          </n-tag>
          <span v-if="note.tags.length > 3" class="more-tags">
            +{{ note.tags.length - 3 }}
          </span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="note-actions">
        <!-- 收藏按钮 -->
        <n-button
          text
          size="small"
          :type="note.is_favorite ? 'warning' : 'default'"
          @click.stop="$emit('toggle-favorite', note)"
        >
          <n-icon :size="16">
            <svg viewBox="0 0 24 24">
              <path 
                :fill="note.is_favorite ? '#f0a020' : 'currentColor'"
                d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.45,13.97L5.82,21L12,17.27Z" 
              />
            </svg>
          </n-icon>
        </n-button>

        <!-- 更多操作 -->
        <n-dropdown :options="actionOptions" @select="handleAction">
          <n-button text size="small" @click.stop>
            <n-icon :size="16">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z" />
              </svg>
            </n-icon>
          </n-button>
        </n-dropdown>
      </div>
    </div>

    <!-- 特殊状态指示器 -->
    <div class="note-indicators">
      <!-- TOTP倒计时 -->
      <div v-if="note.note_type === 'totp' && totpInfo" class="totp-indicator">
        <n-progress
          type="circle"
          :percentage="totpInfo.percentage"
          :size="24"
          :show-indicator="false"
          :color="getTotpColor(totpInfo.remaining)"
        />
        <span class="totp-time">{{ totpInfo.remaining }}s</span>
      </div>

      <!-- 过期提醒 -->
      <div v-if="isExpiringSoon(note)" class="expiry-indicator">
        <n-icon size="16" color="#f0a020">
          <svg viewBox="0 0 24 24">
            <path fill="currentColor" d="M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z" />
          </svg>
        </n-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, h } from 'vue';
import {
  NIcon,
  NTag,
  NButton,
  NDropdown,
  NProgress,
  useMessage
} from 'naive-ui';
import type { SecureNote, NoteType } from '../../types';

// Props & Emits
interface Props {
  note: SecureNote;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  click: [note: SecureNote];
  edit: [note: SecureNote];
  delete: [note: SecureNote];
  'toggle-favorite': [note: SecureNote];
}>();

// 状态
const totpInfo = ref<{ remaining: number; percentage: number } | null>(null);
const message = useMessage();

// 操作选项
const actionOptions = [
  {
    label: '编辑',
    key: 'edit',
    icon: () => h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z' })
    ])
  },
  {
    label: '复制',
    key: 'copy',
    icon: () => h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z' })
    ])
  },
  {
    label: '删除',
    key: 'delete',
    icon: () => h('svg', { viewBox: '0 0 24 24' }, [
      h('path', { fill: 'currentColor', d: 'M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z' })
    ])
  }
];

// 计算属性
const getNoteTypeLabel = (type: NoteType): string => {
  const labels: Record<NoteType, string> = {
    login: '登录',
    ssh_key: 'SSH',
    api_key: 'API',
    certificate: '证书',
    database: '数据库',
    totp: 'TOTP',
    tech_note: '笔记',
  };
  return labels[type] || type;
};

const getNoteTypeColor = (type: NoteType): string => {
  const colors: Record<NoteType, string> = {
    login: '#18a058',
    ssh_key: '#2080f0',
    api_key: '#f0a020',
    certificate: '#d03050',
    database: '#7c3aed',
    totp: '#059669',
    tech_note: '#6b7280',
  };
  return colors[type] || '#6b7280';
};

const getNoteTypeTagType = (type: NoteType): string => {
  const types: Record<NoteType, string> = {
    login: 'success',
    ssh_key: 'info',
    api_key: 'warning',
    certificate: 'error',
    database: 'primary',
    totp: 'success',
    tech_note: 'default',
  };
  return types[type] || 'default';
};

const getNoteTypeIcon = (type: NoteType) => {
  // 简化处理，返回通用图标
  return () => h('svg', { viewBox: '0 0 24 24' }, [
    h('path', { fill: 'currentColor', d: 'M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z' })
  ]);
};

// 方法
const getNotePreview = (note: SecureNote): string => {
  const content = note.content;
  
  switch (note.note_type) {
    case 'login':
      return `${content.username || ''}@${content.url || ''}`;
    case 'ssh_key':
      return `${content.username || ''}@${content.host || ''}:${content.port || 22}`;
    case 'api_key':
      return `${content.service || ''} - ${content.description || ''}`;
    case 'database':
      return `${content.db_type || ''} - ${content.host || ''}:${content.port || ''}`;
    case 'totp':
      return `${content.issuer || ''} - ${content.account || ''}`;
    case 'tech_note':
      return content.content?.substring(0, 100) || '';
    default:
      return '';
  }
};

const formatTime = (timeStr: string): string => {
  const time = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - time.getTime();
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  
  return time.toLocaleDateString();
};

const isExpiringSoon = (note: SecureNote): boolean => {
  if (note.note_type === 'certificate' && note.content.expires_at) {
    const expiryDate = new Date(note.content.expires_at);
    const now = new Date();
    const daysUntilExpiry = Math.floor((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  }
  return false;
};

const getTotpColor = (remaining: number): string => {
  if (remaining <= 10) return '#d03050';
  if (remaining <= 20) return '#f0a020';
  return '#18a058';
};

const handleAction = (key: string) => {
  switch (key) {
    case 'edit':
      emit('edit', props.note);
      break;
    case 'copy':
      handleCopy();
      break;
    case 'delete':
      emit('delete', props.note);
      break;
  }
};

const handleCopy = async () => {
  try {
    // 根据笔记类型复制不同的内容
    let textToCopy = '';
    
    switch (props.note.note_type) {
      case 'login':
        textToCopy = `用户名: ${props.note.content.username}\n密码: ${props.note.content.password}`;
        break;
      case 'api_key':
        textToCopy = props.note.content.api_key;
        break;
      default:
        textToCopy = JSON.stringify(props.note.content, null, 2);
    }
    
    await navigator.clipboard.writeText(textToCopy);
    message.success('已复制到剪贴板');
  } catch (error) {
    message.error('复制失败');
  }
};

// TOTP倒计时
const updateTotpInfo = () => {
  if (props.note.note_type === 'totp') {
    const period = props.note.content.period || 30;
    const now = Math.floor(Date.now() / 1000);
    const remaining = period - (now % period);
    const percentage = (remaining / period) * 100;
    
    totpInfo.value = { remaining, percentage };
  }
};

// 生命周期
let totpInterval: number | null = null;

onMounted(() => {
  if (props.note.note_type === 'totp') {
    updateTotpInfo();
    totpInterval = window.setInterval(updateTotpInfo, 1000);
  }
});

onUnmounted(() => {
  if (totpInterval) {
    clearInterval(totpInterval);
  }
});
</script>

<style scoped>
.note-item {
  background: var(--n-card-color);
  border: 1px solid var(--n-border-color);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.note-item:hover {
  border-color: var(--n-primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.note-item--favorite {
  border-left: 4px solid #f0a020;
}

.note-content {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.note-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.note-info {
  flex: 1;
  min-width: 0;
}

.note-header {
  margin-bottom: 8px;
}

.note-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.note-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.note-time {
  font-size: 12px;
  color: var(--n-text-color-3);
}

.note-preview {
  margin-bottom: 8px;
}

.note-preview p {
  margin: 0;
  font-size: 14px;
  color: var(--n-text-color-2);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.note-tags {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.note-tag {
  font-size: 11px;
}

.more-tags {
  font-size: 12px;
  color: var(--n-text-color-3);
}

.note-actions {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
  gap: 4px;
}

.note-indicators {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.totp-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.totp-time {
  font-size: 10px;
  font-weight: 600;
  color: var(--n-text-color-2);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .note-item {
    padding: 12px;
  }
  
  .note-content {
    gap: 8px;
  }
  
  .note-title {
    font-size: 14px;
  }
  
  .note-preview p {
    font-size: 13px;
  }
}
</style>
