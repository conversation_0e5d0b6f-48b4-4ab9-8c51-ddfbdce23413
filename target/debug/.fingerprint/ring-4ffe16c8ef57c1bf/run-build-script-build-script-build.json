{"rustc": 5148484765029645790, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5491919304041016563, "build_script_build", false, 11287523084748963216]], "local": [{"RerunIfChanged": {"output": "debug/build/ring-4ffe16c8ef57c1bf/output", "paths": ["crypto/perlasm/x86_64-xlate.pl", "crypto/perlasm/x86gas.pl", "crypto/perlasm/x86nasm.pl", "crypto/perlasm/arm-xlate.pl", "crypto/perlasm/x86asm.pl", "crypto/internal.h", "crypto/cpu_intel.c", "crypto/chacha/asm/chacha-x86.pl", "crypto/chacha/asm/chacha-armv8.pl", "crypto/chacha/asm/chacha-x86_64.pl", "crypto/chacha/asm/chacha-armv4.pl", "crypto/mem.c", "crypto/cipher/asm/chacha20_poly1305_armv8.pl", "crypto/cipher/asm/chacha20_poly1305_x86_64.pl", "crypto/fipsmodule/ec/ecp_nistz.c", "crypto/fipsmodule/ec/p256-nistz.h", "crypto/fipsmodule/ec/asm/p256-x86_64-asm.pl", "crypto/fipsmodule/ec/asm/p256-armv8-asm.pl", "crypto/fipsmodule/ec/ecp_nistz384.inl", "crypto/fipsmodule/ec/gfp_p256.c", "crypto/fipsmodule/ec/p256-nistz.c", "crypto/fipsmodule/ec/p256_shared.h", "crypto/fipsmodule/ec/ecp_nistz.h", "crypto/fipsmodule/ec/p256-nistz-table.h", "crypto/fipsmodule/ec/util.h", "crypto/fipsmodule/ec/gfp_p384.c", "crypto/fipsmodule/ec/ecp_nistz384.h", "crypto/fipsmodule/ec/p256.c", "crypto/fipsmodule/ec/p256_table.h", "crypto/fipsmodule/bn/internal.h", "crypto/fipsmodule/bn/asm/armv8-mont.pl", "crypto/fipsmodule/bn/asm/x86-mont.pl", "crypto/fipsmodule/bn/asm/x86_64-mont.pl", "crypto/fipsmodule/bn/asm/x86_64-mont5.pl", "crypto/fipsmodule/bn/asm/armv4-mont.pl", "crypto/fipsmodule/bn/montgomery.c", "crypto/fipsmodule/bn/montgomery_inv.c", "crypto/fipsmodule/sha/asm/sha512-armv8.pl", "crypto/fipsmodule/sha/asm/sha512-x86_64.pl", "crypto/fipsmodule/sha/asm/sha256-armv4.pl", "crypto/fipsmodule/sha/asm/sha512-armv4.pl", "crypto/fipsmodule/aes/asm/vpaes-x86_64.pl", "crypto/fipsmodule/aes/asm/vpaes-armv8.pl", "crypto/fipsmodule/aes/asm/vpaes-x86.pl", "crypto/fipsmodule/aes/asm/bsaes-armv7.pl", "crypto/fipsmodule/aes/asm/aesv8-armx.pl", "crypto/fipsmodule/aes/asm/ghashv8-armx.pl", "crypto/fipsmodule/aes/asm/aes-gcm-avx2-x86_64.pl", "crypto/fipsmodule/aes/asm/vpaes-armv7.pl", "crypto/fipsmodule/aes/asm/aesni-x86_64.pl", "crypto/fipsmodule/aes/asm/aesv8-gcm-armv8.pl", "crypto/fipsmodule/aes/asm/ghash-x86.pl", "crypto/fipsmodule/aes/asm/aesni-x86.pl", "crypto/fipsmodule/aes/asm/ghash-neon-armv8.pl", "crypto/fipsmodule/aes/asm/aesni-gcm-x86_64.pl", "crypto/fipsmodule/aes/asm/ghash-x86_64.pl", "crypto/fipsmodule/aes/asm/ghash-armv4.pl", "crypto/fipsmodule/aes/aes_nohw.c", "crypto/constant_time_test.c", "crypto/poly1305/poly1305_arm.c", "crypto/poly1305/poly1305_arm_asm.S", "crypto/poly1305/poly1305.c", "crypto/limbs/limbs.inl", "crypto/limbs/limbs.c", "crypto/limbs/limbs.h", "crypto/curve25519/internal.h", "crypto/curve25519/asm/x25519-asm-arm.S", "crypto/curve25519/curve25519_64_adx.c", "crypto/curve25519/curve25519.c", "crypto/curve25519/curve25519_tables.h", "crypto/crypto.c", "include/ring-core/asm_base.h", "include/ring-core/type_check.h", "include/ring-core/aes.h", "include/ring-core/check.h", "include/ring-core/target.h", "include/ring-core/mem.h", "include/ring-core/base.h", "third_party/fiat/asm/fiat_curve25519_adx_mul.S", "third_party/fiat/asm/fiat_curve25519_adx_square.S", "third_party/fiat/LICENSE", "third_party/fiat/curve25519_64.h", "third_party/fiat/p256_64.h", "third_party/fiat/curve25519_64_adx.h", "third_party/fiat/curve25519_64_msvc.h", "third_party/fiat/p256_64_msvc.h", "third_party/fiat/p256_32.h", "third_party/fiat/curve25519_32.h"]}}, {"RerunIfEnvChanged": {"var": "CARGO_MANIFEST_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_PKG_NAME", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_PKG_VERSION_MAJOR", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_PKG_VERSION_MINOR", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_PKG_VERSION_PATCH", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_PKG_VERSION_PRE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_MANIFEST_LINKS", "val": null}}, {"RerunIfEnvChanged": {"var": "RING_PREGENERATE_ASM", "val": null}}, {"RerunIfEnvChanged": {"var": "OUT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_ARCH", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_OS", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_ENV", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_ENDIAN", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "MACOSX_DEPLOYMENT_TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "MACOSX_DEPLOYMENT_TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-apple-darwin", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}