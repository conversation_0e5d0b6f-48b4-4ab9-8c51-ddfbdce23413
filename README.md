# SecureFox Password Manager

面向个人开发者/技术人员的零知识加密密钥管理工具。

## 项目结构

```
securefox/
├── backend/          # Rust Axum 后端
├── extension/        # WXT 浏览器扩展
├── shared/           # 共享类型定义
└── docs/            # 项目文档
```

## 技术栈

### 后端 (Rust)
- **Web框架**: Axum 0.7
- **数据库**: SQLite + sqlx
- **加密**: ring 0.17
- **认证**: jsonwebtoken 9.0

### 前端 (WXT + Vue 3)
- **框架**: WXT + Vue 3 + TypeScript
- **UI库**: Naive UI
- **状态管理**: Pinia
- **代码编辑**: Monaco Editor

## 开发环境设置

### 前置要求
- Rust 1.70+
- Node.js 18+
- Chrome/Firefox 浏览器

### 后端开发
```bash
cd backend
cargo run
```

### 扩展开发
```bash
cd extension
npm install
npm run dev
```

## 核心功能

- 🔐 零知识加密架构
- 🔑 统一密钥管理 (SSH、API、证书等)
- 🌐 浏览器自动填充
- 📱 TOTP 两步验证
- 🔍 全文搜索
- 📤 数据导入导出

## 安全特性

- AES-256-GCM 加密
- PBKDF2-SHA256 密钥派生
- 客户端加密，服务端盲存
- 内存安全 (Rust)

## 许可证

MIT License
