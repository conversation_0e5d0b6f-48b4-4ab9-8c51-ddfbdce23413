// {{RIPER-5:
//   Action: "Modified"
//   Task_ID: "#a194fea0-d065-4b72-85be-8e349bb0f85e"
//   Timestamp: "2025-08-05T16:40:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "Background script功能完整，支持消息路由和自动锁定"
// }}
// Background Script - 后台服务

import { messageRouter, MessageType } from '../../utils/messaging';
import { initializeApiClient } from '../../utils/api';
import { setupStorageListeners, AuthStorage, ActivityStorage, SettingsStorage } from '../../utils/storage';

console.log('SecureFox background script loaded');

// 初始化
async function initialize() {
  try {
    // 初始化API客户端
    await initializeApiClient();

    // 设置存储监听器
    setupStorageListeners();

    // 设置消息路由器
    setupMessageHandlers();

    // 启动消息监听
    messageRouter.startListening();

    // 设置自动锁定检查
    setupAutoLockCheck();

    console.log('Background script initialized successfully');
  } catch (error) {
    console.error('Failed to initialize background script:', error);
  }
}

// 设置消息处理器
function setupMessageHandlers() {
  // 获取活动标签页
  messageRouter.register(MessageType.GET_ACTIVE_TAB, async () => {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      return { success: true, data: tabs[0] || null };
    } catch (error) {
      return { success: false, error: 'Failed to get active tab' };
    }
  });

  // 显示通知
  messageRouter.register(MessageType.SHOW_NOTIFICATION, async (payload) => {
    try {
      const { title, message, type = 'basic' } = payload;

      await chrome.notifications.create({
        type,
        iconUrl: '/icon-48.png',
        title,
        message,
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to show notification' };
    }
  });

  // Ping响应
  messageRouter.register(MessageType.PING, async () => {
    return { success: true, data: 'Background script is active' };
  });
}

// 设置自动锁定检查
function setupAutoLockCheck() {
  // 每分钟检查一次自动锁定
  setInterval(async () => {
    try {
      const shouldLock = await ActivityStorage.shouldAutoLock();

      if (shouldLock) {
        // 清除认证状态
        await AuthStorage.clearAuthState();

        // 通知所有popup实例
        try {
          await chrome.runtime.sendMessage({
            type: 'AUTO_LOCK_TRIGGERED',
          });
        } catch (error) {
          // 忽略错误，可能没有popup在监听
        }

        console.log('Auto-lock triggered');
      }
    } catch (error) {
      console.error('Auto-lock check failed:', error);
    }
  }, 60000); // 每分钟检查一次
}

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('SecureFox extension installed:', details.reason);

  if (details.reason === 'install') {
    // 首次安装时的初始化逻辑
    console.log('First time installation');

    try {
      // 初始化默认设置
      const settings = await SettingsStorage.getSettings();
      await SettingsStorage.setSettings(settings);

      // 创建右键菜单
      await createContextMenus();

      console.log('Extension initialized successfully');
    } catch (error) {
      console.error('Failed to initialize extension:', error);
    }
  }
});

// 创建右键菜单
async function createContextMenus() {
  try {
    // 清除现有菜单
    await chrome.contextMenus.removeAll();

    // 创建主菜单
    chrome.contextMenus.create({
      id: 'securefox-main',
      title: 'SecureFox',
      contexts: ['page', 'selection'],
    });

    // 创建子菜单
    chrome.contextMenus.create({
      id: 'securefox-fill',
      parentId: 'securefox-main',
      title: '自动填充',
      contexts: ['page'],
    });

    chrome.contextMenus.create({
      id: 'securefox-generate',
      parentId: 'securefox-main',
      title: '生成密码',
      contexts: ['page', 'selection'],
    });

    chrome.contextMenus.create({
      id: 'securefox-save',
      parentId: 'securefox-main',
      title: '保存登录信息',
      contexts: ['page'],
    });
  } catch (error) {
    console.error('Failed to create context menus:', error);
  }
}

// 处理右键菜单点击
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  if (!tab?.id) return;

  try {
    switch (info.menuItemId) {
      case 'securefox-fill':
        // 发送自动填充消息到content script
        await chrome.tabs.sendMessage(tab.id, {
          type: MessageType.FILL_FORM,
        });
        break;

      case 'securefox-generate':
        // 打开密码生成器
        await chrome.action.openPopup();
        break;

      case 'securefox-save':
        // 发送保存凭据消息到content script
        await chrome.tabs.sendMessage(tab.id, {
          type: MessageType.CAPTURE_CREDENTIALS,
        });
        break;
    }
  } catch (error) {
    console.error('Context menu action failed:', error);
  }
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    try {
      // 检查是否需要自动填充
      const isAuthenticated = await AuthStorage.isAuthenticated();

      if (isAuthenticated) {
        // 发送页面加载完成消息到content script
        await chrome.tabs.sendMessage(tabId, {
          type: 'PAGE_LOADED',
          url: tab.url,
        });
      }
    } catch (error) {
      // 忽略错误，可能content script还未注入
    }
  }
});

// 启动初始化
initialize();

export {};
