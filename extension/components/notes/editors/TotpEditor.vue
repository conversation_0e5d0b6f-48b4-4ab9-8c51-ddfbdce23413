<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "TOTP编辑器组件基础实现"
}} -->
<template>
  <div class="totp-editor">
    <n-space vertical size="medium">
      <n-form-item label="密钥" path="secret">
        <n-input v-model:value="localValue.secret" :disabled="disabled" />
      </n-form-item>
      <n-form-item label="发行方" path="issuer">
        <n-input v-model:value="localValue.issuer" :disabled="disabled" />
      </n-form-item>
      <n-form-item label="账户" path="account">
        <n-input v-model:value="localValue.account" :disabled="disabled" />
      </n-form-item>
      <n-grid :cols="3" :x-gap="16">
        <n-grid-item>
          <n-form-item label="算法" path="algorithm">
            <n-select v-model:value="localValue.algorithm" :options="algorithmOptions" :disabled="disabled" />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="位数" path="digits">
            <n-input-number v-model:value="localValue.digits" :min="6" :max="8" :disabled="disabled" />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="周期" path="period">
            <n-input-number v-model:value="localValue.period" :min="15" :max="60" :disabled="disabled" />
          </n-form-item>
        </n-grid-item>
      </n-grid>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { NSpace, NFormItem, NInput, NSelect, NInputNumber, NGrid, NGridItem } from 'naive-ui';
import type { TotpContent } from '../../../types';

interface Props {
  value: TotpContent;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), { disabled: false });
const emit = defineEmits<{ 'update:value': [value: TotpContent] }>();
const localValue = ref<TotpContent>({ ...props.value });

const algorithmOptions = [
  { label: 'SHA1', value: 'SHA1' },
  { label: 'SHA256', value: 'SHA256' },
  { label: 'SHA512', value: 'SHA512' },
];

watch(localValue, (newValue) => emit('update:value', { ...newValue }), { deep: true });
watch(() => props.value, (newValue) => { localValue.value = { ...newValue }; }, { deep: true });
</script>

<style scoped>
.totp-editor { width: 100%; }
</style>
