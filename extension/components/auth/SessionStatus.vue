<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#b290202c-b04c-4580-9b7a-7c86848ed54f"
  Timestamp: "2025-08-05T17:00:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "会话状态组件完整，支持自动锁定倒计时和会话延长"
}} -->
<template>
  <div class="session-status">
    <!-- 会话警告 -->
    <n-alert
      v-if="auth.isSessionWarningShown.value"
      type="warning"
      :show-icon="false"
      closable
      @close="handleDismissWarning"
    >
      <template #header>
        <n-space align="center">
          <n-icon size="18">
            <svg viewBox="0 0 24 24">
              <path fill="currentColor" d="M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z" />
            </svg>
          </n-icon>
          <span>会话即将过期</span>
        </n-space>
      </template>
      
      <div class="warning-content">
        <p>您的会话将在 {{ formatTime(auth.sessionTimeRemaining.value) }} 后过期</p>
        <n-space>
          <n-button size="small" type="primary" @click="handleExtendSession">
            延长会话
          </n-button>
          <n-button size="small" @click="handleLogout">
            立即登出
          </n-button>
        </n-space>
      </div>
    </n-alert>

    <!-- 会话信息卡片 -->
    <n-card 
      v-if="showSessionInfo && sessionInfo"
      size="small" 
      title="会话信息"
      class="session-info-card"
    >
      <template #header-extra>
        <n-button 
          text 
          size="small"
          @click="showSessionInfo = false"
        >
          <n-icon>
            <svg viewBox="0 0 24 24">
              <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
            </svg>
          </n-icon>
        </n-button>
      </template>

      <n-space vertical size="small">
        <!-- 用户信息 -->
        <div class="info-item">
          <n-text depth="3">登录用户：</n-text>
          <n-text>{{ auth.user.value?.email }}</n-text>
        </div>

        <!-- 最后活动时间 -->
        <div class="info-item">
          <n-text depth="3">最后活动：</n-text>
          <n-text>{{ formatLastActivity(sessionInfo.lastActivity) }}</n-text>
        </div>

        <!-- 自动锁定设置 -->
        <div class="info-item">
          <n-text depth="3">自动锁定：</n-text>
          <n-text>
            {{ sessionInfo.autoLockEnabled ? `${sessionInfo.autoLockTimeout}分钟后` : '已禁用' }}
          </n-text>
        </div>

        <!-- 剩余时间 -->
        <div v-if="sessionInfo.autoLockEnabled && sessionInfo.timeRemaining > 0" class="info-item">
          <n-text depth="3">剩余时间：</n-text>
          <n-text :type="getTimeRemainingType(sessionInfo.timeRemaining)">
            {{ formatTime(sessionInfo.timeRemaining) }}
          </n-text>
        </div>

        <!-- 操作按钮 -->
        <n-space>
          <n-button size="small" @click="handleExtendSession">
            <n-icon size="14">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
              </svg>
            </n-icon>
            延长会话
          </n-button>
          <n-button size="small" @click="handleRefreshInfo">
            <n-icon size="14">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
              </svg>
            </n-icon>
            刷新
          </n-button>
        </n-space>
      </n-space>
    </n-card>

    <!-- 快速操作按钮 -->
    <div class="quick-actions">
      <n-button
        v-if="!showSessionInfo"
        text
        size="small"
        @click="handleShowSessionInfo"
      >
        <n-icon size="14">
          <svg viewBox="0 0 24 24">
            <path fill="currentColor" d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z" />
          </svg>
        </n-icon>
        会话信息
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import {
  NAlert,
  NCard,
  NButton,
  NIcon,
  NSpace,
  NText,
  useMessage
} from 'naive-ui';
import { useAuth } from '../../composables/useAuth';

// 组合函数
const auth = useAuth();
const message = useMessage();

// 响应式状态
const showSessionInfo = ref(false);
const sessionInfo = ref<any>(null);

// 定时器
let refreshInterval: number | null = null;

// 方法
const handleExtendSession = async () => {
  try {
    await auth.extendSession();
    await refreshSessionInfo();
  } catch (error) {
    console.error('Failed to extend session:', error);
    message.error('延长会话失败');
  }
};

const handleLogout = async () => {
  try {
    await auth.logout();
  } catch (error) {
    console.error('Failed to logout:', error);
  }
};

const handleDismissWarning = () => {
  // 警告会通过auth store自动管理
};

const handleShowSessionInfo = async () => {
  await refreshSessionInfo();
  showSessionInfo.value = true;
};

const handleRefreshInfo = async () => {
  await refreshSessionInfo();
  message.success('会话信息已刷新');
};

const refreshSessionInfo = async () => {
  try {
    sessionInfo.value = await auth.getSessionInfo();
  } catch (error) {
    console.error('Failed to get session info:', error);
  }
};

// 格式化时间
const formatTime = (seconds: number): string => {
  if (seconds <= 0) return '已过期';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`;
  } else {
    return `${secs}秒`;
  }
};

const formatLastActivity = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}小时前`;
  
  return date.toLocaleString();
};

const getTimeRemainingType = (seconds: number): 'default' | 'warning' | 'error' => {
  if (seconds <= 120) return 'error';  // 2分钟内
  if (seconds <= 300) return 'warning'; // 5分钟内
  return 'default';
};

// 生命周期
onMounted(() => {
  // 每30秒刷新一次会话信息
  refreshInterval = window.setInterval(() => {
    if (showSessionInfo.value) {
      refreshSessionInfo();
    }
  }, 30000);
});

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
});
</script>

<style scoped>
.session-status {
  width: 100%;
}

.warning-content {
  margin-top: 8px;
}

.warning-content p {
  margin: 0 0 12px 0;
  font-size: 14px;
}

.session-info-card {
  margin-top: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.quick-actions {
  margin-top: 8px;
  text-align: center;
}

:deep(.n-alert) {
  margin-bottom: 0;
}

:deep(.n-card-header) {
  padding-bottom: 8px;
}

:deep(.n-card-content) {
  padding-top: 0;
}
</style>
