use axum::{
    http::StatusCode,
    response::Json,
    routing::get,
    Router,
};
use serde_json::{json, Value};
use std::net::SocketAddr;
use tower_http::cors::CorsLayer;
use tracing::{info, Level};
use tracing_subscriber;

mod config;
mod auth;
mod crypto;
pub mod database;
mod notes;
mod api;

use config::Config;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .init();

    // 加载配置
    let config = Config::load()?;
    info!("Configuration loaded successfully");

    // 创建应用路由
    let app = create_app().await?;

    // 启动服务器
    let addr = SocketAddr::from(([127, 0, 0, 1], config.port));
    info!("Server starting on {}", addr);

    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

async fn create_app() -> anyhow::Result<Router> {
    // 初始化数据库
    let config = Config::load()?;
    let db_manager = database::DatabaseManager::new(&config.database_url).await?;

    // 数据库健康检查
    db_manager.health_check().await?;
    info!("Database health check passed");

    // 创建API路由
    let api_routes = api::create_routes();

    let app = Router::new()
        .route("/", get(health_check))
        .route("/health", get(health_check))
        .route("/health/db", get(db_health_check))
        .merge(api_routes)
        .with_state(db_manager)
        .layer(CorsLayer::permissive());

    Ok(app)
}

async fn health_check() -> Result<Json<Value>, StatusCode> {
    Ok(Json(json!({
        "status": "ok",
        "service": "securefox-backend",
        "version": env!("CARGO_PKG_VERSION")
    })))
}

async fn db_health_check(
    axum::extract::State(db_manager): axum::extract::State<database::DatabaseManager>,
) -> Result<Json<Value>, StatusCode> {
    match db_manager.health_check().await {
        Ok(_) => Ok(Json(json!({
            "status": "ok",
            "database": "connected"
        }))),
        Err(_) => Err(StatusCode::SERVICE_UNAVAILABLE),
    }
}
