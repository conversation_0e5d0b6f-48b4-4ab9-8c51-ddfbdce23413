<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "SSH密钥编辑器组件基础实现"
}} -->
<template>
  <div class="ssh-key-editor">
    <n-space vertical size="medium">
      <!-- 密钥类型 -->
      <n-form-item label="密钥类型" path="key_type">
        <n-select
          v-model:value="localValue.key_type"
          :options="keyTypeOptions"
          :disabled="disabled"
        />
      </n-form-item>

      <!-- 私钥 -->
      <n-form-item label="私钥" path="private_key">
        <n-input
          v-model:value="localValue.private_key"
          type="textarea"
          placeholder="-----BEGIN OPENSSH PRIVATE KEY-----"
          :rows="6"
          :disabled="disabled"
        />
      </n-form-item>

      <!-- 公钥 -->
      <n-form-item label="公钥" path="public_key">
        <n-input
          v-model:value="localValue.public_key"
          type="textarea"
          placeholder="ssh-ed25519 AAAAC3NzaC1lZDI1NTE5..."
          :rows="3"
          :disabled="disabled"
        />
      </n-form-item>

      <!-- 服务器列表 -->
      <n-form-item label="服务器" path="servers">
        <n-dynamic-input
          v-model:value="localValue.servers"
          placeholder="添加服务器地址..."
          :disabled="disabled"
        />
      </n-form-item>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { NSpace, NFormItem, NInput, NSelect, NDynamicInput } from 'naive-ui';
import type { SshKeyContent } from '../../../types';

interface Props {
  value: SshKeyContent;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});

const emit = defineEmits<{
  'update:value': [value: SshKeyContent];
}>();

const localValue = ref<SshKeyContent>({ ...props.value });

const keyTypeOptions = [
  { label: 'ED25519', value: 'ed25519' },
  { label: 'RSA', value: 'rsa' },
  { label: 'ECDSA', value: 'ecdsa' },
];

watch(localValue, (newValue) => {
  emit('update:value', { ...newValue });
}, { deep: true });

watch(() => props.value, (newValue) => {
  localValue.value = { ...newValue };
}, { deep: true });
</script>

<style scoped>
.ssh-key-editor {
  width: 100%;
}
</style>
