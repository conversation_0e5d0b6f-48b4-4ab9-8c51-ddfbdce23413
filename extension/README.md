# SecureFox Browser Extension

## 项目概述

SecureFox 是一个面向开发者的安全密钥管理浏览器扩展，基于 WXT 框架开发，使用 Vue 3 + TypeScript + Naive UI 技术栈。

## 技术架构

### 核心技术栈
- **WXT**: 现代浏览器扩展开发框架
- **Vue 3**: 响应式前端框架
- **TypeScript**: 类型安全的 JavaScript
- **Naive UI**: 现代化 UI 组件库
- **Pinia**: Vue 状态管理库

### 项目结构

```
extension/
├── entrypoints/           # 扩展入口点
│   ├── popup/            # 弹出窗口
│   ├── background/       # 后台脚本
│   └── content/          # 内容脚本
├── types/                # TypeScript 类型定义
├── utils/                # 工具函数
│   ├── api.ts           # API 客户端
│   ├── messaging.ts     # 消息通信
│   └── storage.ts       # 存储管理
├── stores/               # Pinia 状态管理
│   ├── auth.ts          # 认证状态
│   └── notes.ts         # 笔记状态
└── public/               # 静态资源
```

## 功能模块

### 1. 认证系统
- 用户注册/登录
- JWT Token 管理
- 自动锁定机制
- 密码强度验证

### 2. 状态管理
- **AuthStore**: 用户认证状态管理
- **NotesStore**: 笔记数据状态管理
- 持久化存储集成

### 3. 消息通信
- Background ↔ Popup 通信
- Background ↔ Content Script 通信
- 统一消息路由系统

### 4. 存储系统
- Chrome Storage API 封装
- 加密数据存储
- 缓存管理
- 自动清理机制

### 5. API 集成
- RESTful API 客户端
- 自动重试机制
- 错误处理
- Token 自动管理

## 开发指南

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 类型检查
```bash
npm run type-check
```

## 扩展架构说明

### Background Script
- 消息路由处理
- 自动锁定检查
- 右键菜单管理
- 标签页监听

### Content Script
- 表单检测
- 自动填充
- 凭据捕获
- 页面交互

### Popup
- 用户界面
- 状态展示
- 快速操作
- 设置管理

## 安全特性

1. **端到端加密**: 所有敏感数据在本地加密存储
2. **自动锁定**: 支持基于时间的自动锁定
3. **安全通信**: 扩展内部组件间安全消息传递
4. **权限最小化**: 仅请求必要的浏览器权限

## 开发状态

### ✅ 已完成
- [x] WXT 基础框架搭建
- [x] Vue 3 + TypeScript 配置
- [x] Pinia 状态管理集成
- [x] 认证系统基础架构
- [x] API 客户端封装
- [x] 消息通信机制
- [x] 存储系统封装
- [x] 基础 UI 界面

### 🚧 开发中
- [ ] 自动填充功能
- [ ] 密码生成器
- [ ] 笔记详情界面
- [ ] 设置页面
- [ ] TOTP 生成器界面

### 📋 待开发
- [ ] 导入/导出功能
- [ ] 主题系统
- [ ] 多语言支持
- [ ] 高级搜索
- [ ] 数据同步

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
