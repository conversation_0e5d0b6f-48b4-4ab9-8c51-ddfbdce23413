<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "数据库编辑器组件基础实现"
}} -->
<template>
  <div class="database-editor">
    <n-space vertical size="medium">
      <n-grid :cols="2" :x-gap="16">
        <n-grid-item>
          <n-form-item label="主机" path="host">
            <n-input v-model:value="localValue.host" :disabled="disabled" />
          </n-form-item>
        </n-grid-item>
        <n-grid-item>
          <n-form-item label="端口" path="port">
            <n-input-number v-model:value="localValue.port" :disabled="disabled" />
          </n-form-item>
        </n-grid-item>
      </n-grid>
      <n-form-item label="数据库名" path="database">
        <n-input v-model:value="localValue.database" :disabled="disabled" />
      </n-form-item>
      <n-form-item label="用户名" path="username">
        <n-input v-model:value="localValue.username" :disabled="disabled" />
      </n-form-item>
      <n-form-item label="密码" path="password">
        <n-input v-model:value="localValue.password" type="password" :disabled="disabled" />
      </n-form-item>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { NSpace, NFormItem, NInput, NInputNumber, NGrid, NGridItem } from 'naive-ui';
import type { DatabaseContent } from '../../../types';

interface Props {
  value: DatabaseContent;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), { disabled: false });
const emit = defineEmits<{ 'update:value': [value: DatabaseContent] }>();
const localValue = ref<DatabaseContent>({ ...props.value });

watch(localValue, (newValue) => emit('update:value', { ...newValue }), { deep: true });
watch(() => props.value, (newValue) => { localValue.value = { ...newValue }; }, { deep: true });
</script>

<style scoped>
.database-editor { width: 100%; }
</style>
