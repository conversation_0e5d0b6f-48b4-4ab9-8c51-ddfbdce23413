<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#b290202c-b04c-4580-9b7a-7c86848ed54f"
  Timestamp: "2025-08-05T17:00:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "认证容器组件完整，支持登录注册切换和会话管理"
}} -->
<template>
  <div class="auth-container">
    <n-card class="auth-card" :bordered="false">
      <!-- 登录表单 -->
      <LoginForm
        v-if="!showRegister"
        :loading="authStore.isLoading"
        :error="authStore.error"
        @submit="handleLogin"
        @switch-to-register="showRegister = true"
        @forgot-password="handleForgotPassword"
        @clear-error="authStore.clearError"
      />

      <!-- 注册表单 -->
      <RegisterForm
        v-else
        :loading="authStore.isLoading"
        :error="authStore.error"
        @submit="handleRegister"
        @switch-to-login="showRegister = false"
        @clear-error="authStore.clearError"
        @show-terms="handleShowTerms"
        @show-privacy="handleShowPrivacy"
      />
    </n-card>

    <!-- 服务条款模态框 -->
    <n-modal
      v-model:show="showTermsModal"
      preset="card"
      title="服务条款"
      style="width: 600px; max-width: 90vw;"
      :mask-closable="false"
    >
      <div class="terms-content">
        <h3>SecureFox 服务条款</h3>
        <p><strong>最后更新：2025年8月5日</strong></p>
        
        <h4>1. 服务说明</h4>
        <p>SecureFox 是一个开源的密码管理器，旨在为用户提供安全的密码存储和管理服务。</p>
        
        <h4>2. 用户责任</h4>
        <ul>
          <li>您有责任保护您的主密码安全</li>
          <li>您需要定期备份您的数据</li>
          <li>您不得将本服务用于非法目的</li>
        </ul>
        
        <h4>3. 隐私保护</h4>
        <p>我们采用零知识加密架构，无法访问您的明文数据。</p>
        
        <h4>4. 免责声明</h4>
        <p>本服务按"现状"提供，我们不对数据丢失承担责任。</p>
      </div>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showTermsModal = false">
            关闭
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 隐私政策模态框 -->
    <n-modal
      v-model:show="showPrivacyModal"
      preset="card"
      title="隐私政策"
      style="width: 600px; max-width: 90vw;"
      :mask-closable="false"
    >
      <div class="privacy-content">
        <h3>SecureFox 隐私政策</h3>
        <p><strong>最后更新：2025年8月5日</strong></p>
        
        <h4>1. 信息收集</h4>
        <p>我们仅收集必要的账户信息（邮箱地址）用于身份验证。</p>
        
        <h4>2. 数据加密</h4>
        <p>所有敏感数据都在本地使用AES-256-GCM加密，我们无法访问您的明文数据。</p>
        
        <h4>3. 数据存储</h4>
        <p>加密后的数据存储在我们的服务器上，但我们无法解密。</p>
        
        <h4>4. 数据共享</h4>
        <p>我们不会与第三方共享您的个人信息。</p>
        
        <h4>5. 数据删除</h4>
        <p>您可以随时删除您的账户和所有相关数据。</p>
      </div>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="showPrivacyModal = false">
            关闭
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 忘记密码模态框 -->
    <n-modal
      v-model:show="showForgotPasswordModal"
      preset="card"
      title="忘记密码"
      style="width: 400px; max-width: 90vw;"
      :mask-closable="false"
    >
      <div class="forgot-password-content">
        <n-alert type="warning" :show-icon="false">
          <template #header>
            <n-icon size="20">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z" />
              </svg>
            </n-icon>
            重要提醒
          </template>
          由于采用零知识加密架构，我们无法帮您找回丢失的主密码。如果您忘记了主密码，将无法访问您的数据。
        </n-alert>
        
        <div style="margin-top: 16px;">
          <p><strong>建议的解决方案：</strong></p>
          <ul>
            <li>尝试您可能使用的其他密码</li>
            <li>检查您的密码管理器或笔记</li>
            <li>如果确实无法找回，您需要创建新账户</li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <n-space justify="space-between">
          <n-button @click="showForgotPasswordModal = false">
            我再想想
          </n-button>
          <n-button type="primary" @click="handleCreateNewAccount">
            创建新账户
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NCard, NModal, NButton, NSpace, NAlert, NIcon, useMessage } from 'naive-ui';
import { useAuthStore } from '../../stores/auth';
import LoginForm from './LoginForm.vue';
import RegisterForm from './RegisterForm.vue';
import type { LoginRequest, RegisterRequest } from '../../types';

// 状态管理
const authStore = useAuthStore();
const message = useMessage();

// 响应式数据
const showRegister = ref(false);
const showTermsModal = ref(false);
const showPrivacyModal = ref(false);
const showForgotPasswordModal = ref(false);

// 方法
const handleLogin = async (data: LoginRequest & { rememberMe: boolean }) => {
  const { rememberMe, ...loginData } = data;
  
  // 如果选择记住登录状态，保存邮箱
  if (rememberMe) {
    await chrome.storage.local.set({ rememberedEmail: loginData.email });
  } else {
    await chrome.storage.local.remove(['rememberedEmail']);
  }
  
  const success = await authStore.login(loginData);
  
  if (success) {
    message.success('登录成功！');
  }
};

const handleRegister = async (data: RegisterRequest) => {
  const success = await authStore.register(data);
  
  if (success) {
    message.success('注册成功！');
  }
};

const handleForgotPassword = () => {
  showForgotPasswordModal.value = true;
};

const handleShowTerms = () => {
  showTermsModal.value = true;
};

const handleShowPrivacy = () => {
  showPrivacyModal.value = true;
};

const handleCreateNewAccount = () => {
  showForgotPasswordModal.value = false;
  showRegister.value = true;
  message.info('请创建新账户');
};

// 生命周期
onMounted(() => {
  // 检查是否有记住的邮箱
  chrome.storage.local.get(['rememberedEmail']).then((result) => {
    if (result.rememberedEmail) {
      // 如果有记住的邮箱，默认显示登录表单
      showRegister.value = false;
    }
  });
});
</script>

<style scoped>
.auth-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 500px;
}

.auth-card {
  width: 100%;
  max-width: 420px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  overflow: hidden;
}

.terms-content,
.privacy-content {
  max-height: 400px;
  overflow-y: auto;
  line-height: 1.6;
}

.terms-content h3,
.privacy-content h3 {
  margin-top: 0;
  color: var(--n-text-color);
}

.terms-content h4,
.privacy-content h4 {
  margin-top: 20px;
  margin-bottom: 8px;
  color: var(--n-text-color);
}

.terms-content p,
.privacy-content p {
  margin-bottom: 12px;
  color: var(--n-text-color-2);
}

.terms-content ul,
.privacy-content ul {
  margin-bottom: 12px;
  padding-left: 20px;
}

.terms-content li,
.privacy-content li {
  margin-bottom: 4px;
  color: var(--n-text-color-2);
}

.forgot-password-content {
  line-height: 1.6;
}

.forgot-password-content p {
  margin-bottom: 8px;
  color: var(--n-text-color);
}

.forgot-password-content ul {
  margin-bottom: 0;
  padding-left: 20px;
}

.forgot-password-content li {
  margin-bottom: 4px;
  color: var(--n-text-color-2);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .auth-container {
    padding: 8px;
  }
  
  .auth-card {
    max-width: 100%;
  }
}
</style>
