// {{RIPER-5: Action:"Added", Task_ID:"#8636e378", Timestamp:"2025-08-05", Role:"LD", Principle:"SOLID-S"}}
// 密钥派生实现 - PBKDF2-SHA256 密钥派生服务

use ring::{digest, pbkdf2};
use std::num::NonZeroU32;
use anyhow::{Result, anyhow};
use zeroize::{Zeroize, ZeroizeOnDrop};

/// PBKDF2 迭代次数 - 100,000次以确保安全性
const PBKDF2_ITERATIONS: u32 = 100_000;

/// 主密钥长度 (256位 = 32字节)
pub const MASTER_KEY_LENGTH: usize = 32;

/// 盐值长度 (128位 = 16字节)
pub const SALT_LENGTH: usize = 16;

/// 安全的主密钥结构，自动清零
#[derive(Clone, ZeroizeOnDrop)]
pub struct MasterKey {
    key: [u8; MASTER_KEY_LENGTH],
}

impl MasterKey {
    /// 创建新的主密钥
    pub fn new(key: [u8; MASTER_KEY_LENGTH]) -> Self {
        Self { key }
    }

    /// 获取密钥字节
    pub fn as_bytes(&self) -> &[u8] {
        &self.key
    }

    /// 从主密钥派生会话密钥
    pub fn derive_session_key(&self, context: &str) -> Result<SessionKey> {
        let mut session_key = [0u8; 32];

        // 使用HKDF从主密钥派生会话密钥
        let salt = digest::digest(&digest::SHA256, context.as_bytes());
        let prk = ring::hkdf::Salt::new(ring::hkdf::HKDF_SHA256, salt.as_ref())
            .extract(&self.key);

        prk.expand(&[context.as_bytes()], ring::hkdf::HKDF_SHA256)
            .map_err(|_| anyhow!("Failed to derive session key"))?
            .fill(&mut session_key)
            .map_err(|_| anyhow!("Failed to fill session key"))?;

        Ok(SessionKey::new(session_key))
    }
}

/// 会话密钥结构，自动清零
#[derive(Clone, ZeroizeOnDrop)]
pub struct SessionKey {
    key: [u8; 32],
}

impl SessionKey {
    /// 创建新的会话密钥
    pub fn new(key: [u8; 32]) -> Self {
        Self { key }
    }

    /// 获取密钥字节
    pub fn as_bytes(&self) -> &[u8] {
        &self.key
    }
}

/// 密钥派生服务
pub struct KeyDerivationService;

impl KeyDerivationService {
    /// 从主密码派生主密钥
    ///
    /// # 参数
    /// * `master_password` - 用户的主密码
    /// * `salt` - 随机盐值 (16字节)
    ///
    /// # 返回
    /// * `Result<MasterKey>` - 派生的主密钥
    pub fn derive_master_key(master_password: &str, salt: &[u8]) -> Result<MasterKey> {
        if salt.len() != SALT_LENGTH {
            return Err(anyhow!("Invalid salt length: expected {}, got {}", SALT_LENGTH, salt.len()));
        }

        let mut master_key = [0u8; MASTER_KEY_LENGTH];

        // 使用PBKDF2-SHA256派生密钥
        let iterations = NonZeroU32::new(PBKDF2_ITERATIONS)
            .ok_or_else(|| anyhow!("Invalid iteration count"))?;

        pbkdf2::derive(
            pbkdf2::PBKDF2_HMAC_SHA256,
            iterations,
            salt,
            master_password.as_bytes(),
            &mut master_key,
        );

        Ok(MasterKey::new(master_key))
    }

    /// 验证主密码
    ///
    /// # 参数
    /// * `master_password` - 用户输入的主密码
    /// * `salt` - 存储的盐值
    /// * `stored_key` - 存储的主密钥
    ///
    /// # 返回
    /// * `Result<bool>` - 验证结果
    pub fn verify_master_password(
        master_password: &str,
        salt: &[u8],
        stored_key: &[u8],
    ) -> Result<bool> {
        let derived_key = Self::derive_master_key(master_password, salt)?;

        // 使用常时间比较防止时间攻击
        Ok(ring::constant_time::verify_slices_are_equal(
            derived_key.as_bytes(),
            stored_key,
        ).is_ok())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rand::RngCore;

    #[test]
    fn test_derive_master_key() {
        let password = "test_password_123!";
        let mut salt = [0u8; SALT_LENGTH];
        rand::thread_rng().fill_bytes(&mut salt);

        let key = KeyDerivationService::derive_master_key(password, &salt).unwrap();
        assert_eq!(key.as_bytes().len(), MASTER_KEY_LENGTH);
    }

    #[test]
    fn test_verify_master_password() {
        let password = "test_password_123!";
        let mut salt = [0u8; SALT_LENGTH];
        rand::thread_rng().fill_bytes(&mut salt);

        let key = KeyDerivationService::derive_master_key(password, &salt).unwrap();

        // 正确密码应该验证成功
        assert!(KeyDerivationService::verify_master_password(password, &salt, key.as_bytes()).unwrap());

        // 错误密码应该验证失败
        assert!(!KeyDerivationService::verify_master_password("wrong_password", &salt, key.as_bytes()).unwrap());
    }

    #[test]
    fn test_derive_session_key() {
        let password = "test_password_123!";
        let mut salt = [0u8; SALT_LENGTH];
        rand::thread_rng().fill_bytes(&mut salt);

        let master_key = KeyDerivationService::derive_master_key(password, &salt).unwrap();
        let session_key = master_key.derive_session_key("encryption").unwrap();

        assert_eq!(session_key.as_bytes().len(), 32);

        // 相同上下文应该产生相同的会话密钥
        let session_key2 = master_key.derive_session_key("encryption").unwrap();
        assert_eq!(session_key.as_bytes(), session_key2.as_bytes());

        // 不同上下文应该产生不同的会话密钥
        let session_key3 = master_key.derive_session_key("authentication").unwrap();
        assert_ne!(session_key.as_bytes(), session_key3.as_bytes());
    }
}
