// {{RIPER-5: Action:"Added", Task_ID:"#8636e378", Timestamp:"2025-08-05", Role:"LD", Principle:"SOLID-S"}}
// 安全内存管理 - 防止敏感数据泄露

use zeroize::{Zeroize, ZeroizeOnDrop};
use std::fmt;

/// 安全字符串，自动清零内存
#[derive(Clone, ZeroizeOnDrop)]
pub struct SecureString {
    data: String,
}

impl SecureString {
    /// 创建新的安全字符串
    pub fn new(data: String) -> Self {
        Self { data }
    }

    /// 从字符串切片创建安全字符串
    pub fn from_str(s: &str) -> Self {
        Self {
            data: s.to_string(),
        }
    }

    /// 获取字符串引用
    pub fn as_str(&self) -> &str {
        &self.data
    }

    /// 获取字符串长度
    pub fn len(&self) -> usize {
        self.data.len()
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }

    /// 清零并重置为空字符串
    pub fn clear(&mut self) {
        self.data.zeroize();
        self.data.clear();
    }
}

impl fmt::Debug for SecureString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "SecureString([REDACTED])")
    }
}

impl fmt::Display for SecureString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[REDACTED]")
    }
}

impl From<String> for SecureString {
    fn from(s: String) -> Self {
        Self::new(s)
    }
}

impl From<&str> for SecureString {
    fn from(s: &str) -> Self {
        Self::from_str(s)
    }
}

/// 安全字节数组，自动清零内存
#[derive(Clone, ZeroizeOnDrop)]
pub struct SecureBytes {
    data: Vec<u8>,
}

impl SecureBytes {
    /// 创建新的安全字节数组
    pub fn new(data: Vec<u8>) -> Self {
        Self { data }
    }

    /// 从字节切片创建安全字节数组
    pub fn from_slice(bytes: &[u8]) -> Self {
        Self {
            data: bytes.to_vec(),
        }
    }

    /// 创建指定长度的零填充安全字节数组
    pub fn zeros(len: usize) -> Self {
        Self {
            data: vec![0u8; len],
        }
    }

    /// 获取字节切片引用
    pub fn as_slice(&self) -> &[u8] {
        &self.data
    }

    /// 获取可变字节切片引用
    pub fn as_mut_slice(&mut self) -> &mut [u8] {
        &mut self.data
    }

    /// 获取长度
    pub fn len(&self) -> usize {
        self.data.len()
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }

    /// 清零并重置为空数组
    pub fn clear(&mut self) {
        self.data.zeroize();
        self.data.clear();
    }

    /// 扩展数组
    pub fn extend_from_slice(&mut self, other: &[u8]) {
        self.data.extend_from_slice(other);
    }
}

impl fmt::Debug for SecureBytes {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "SecureBytes([REDACTED {} bytes])", self.data.len())
    }
}

impl From<Vec<u8>> for SecureBytes {
    fn from(data: Vec<u8>) -> Self {
        Self::new(data)
    }
}

impl From<&[u8]> for SecureBytes {
    fn from(bytes: &[u8]) -> Self {
        Self::from_slice(bytes)
    }
}

/// 内存安全工具
pub struct SecureMemory;

impl SecureMemory {
    /// 安全比较两个字节切片，防止时间攻击
    pub fn constant_time_eq(a: &[u8], b: &[u8]) -> bool {
        if a.len() != b.len() {
            return false;
        }

        ring::constant_time::verify_slices_are_equal(a, b).is_ok()
    }

    /// 安全清零内存区域
    pub fn secure_zero(data: &mut [u8]) {
        data.zeroize();
    }

    /// 创建安全的随机填充
    pub fn secure_random_fill(data: &mut [u8]) -> Result<(), ring::error::Unspecified> {
        use ring::rand::{SecureRandom, SystemRandom};
        let rng = SystemRandom::new();
        rng.fill(data)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_secure_string() {
        let mut secure_str = SecureString::from_str("sensitive_password");

        assert_eq!(secure_str.as_str(), "sensitive_password");
        assert_eq!(secure_str.len(), 18);
        assert!(!secure_str.is_empty());

        // 测试清零
        secure_str.clear();
        assert!(secure_str.is_empty());
        assert_eq!(secure_str.len(), 0);
    }

    #[test]
    fn test_secure_string_debug() {
        let secure_str = SecureString::from_str("secret");
        let debug_str = format!("{:?}", secure_str);

        // 确保调试输出不包含敏感数据
        assert!(!debug_str.contains("secret"));
        assert!(debug_str.contains("REDACTED"));
    }

    #[test]
    fn test_secure_bytes() {
        let data = vec![1, 2, 3, 4, 5];
        let mut secure_bytes = SecureBytes::new(data.clone());

        assert_eq!(secure_bytes.as_slice(), &data);
        assert_eq!(secure_bytes.len(), 5);
        assert!(!secure_bytes.is_empty());

        // 测试清零
        secure_bytes.clear();
        assert!(secure_bytes.is_empty());
        assert_eq!(secure_bytes.len(), 0);
    }

    #[test]
    fn test_secure_bytes_zeros() {
        let secure_bytes = SecureBytes::zeros(10);

        assert_eq!(secure_bytes.len(), 10);
        assert_eq!(secure_bytes.as_slice(), &[0u8; 10]);
    }

    #[test]
    fn test_constant_time_eq() {
        let a = b"hello";
        let b = b"hello";
        let c = b"world";

        assert!(SecureMemory::constant_time_eq(a, b));
        assert!(!SecureMemory::constant_time_eq(a, c));
        assert!(!SecureMemory::constant_time_eq(a, b"hell")); // 不同长度
    }

    #[test]
    fn test_secure_zero() {
        let mut data = vec![1, 2, 3, 4, 5];
        SecureMemory::secure_zero(&mut data);

        assert_eq!(data, vec![0, 0, 0, 0, 0]);
    }

    #[test]
    fn test_secure_random_fill() {
        let mut data1 = vec![0u8; 32];
        let mut data2 = vec![0u8; 32];

        SecureMemory::secure_random_fill(&mut data1).unwrap();
        SecureMemory::secure_random_fill(&mut data2).unwrap();

        // 随机数据应该不同
        assert_ne!(data1, data2);
        // 不应该全是零
        assert_ne!(data1, vec![0u8; 32]);
    }
}
