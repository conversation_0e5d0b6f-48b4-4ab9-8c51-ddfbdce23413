// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#8db4a073-e2f1-4c70-9e28-e31d3e70f268"
//   Timestamp: "2025-08-05T15:30:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "认证模型定义完整，类型安全，支持JWT claims"
// }}
// 认证相关模型定义

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// JWT Claims 结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Claims {
    /// 用户ID
    pub sub: String,
    /// 用户邮箱
    pub email: String,
    /// 签发时间
    pub iat: i64,
    /// 过期时间
    pub exp: i64,
}

impl Claims {
    /// 创建新的JWT Claims
    pub fn new(user_id: &str, email: &str, expires_in_hours: i64) -> Self {
        let now = Utc::now();
        let exp = now + chrono::Duration::hours(expires_in_hours);

        Self {
            sub: user_id.to_string(),
            email: email.to_string(),
            iat: now.timestamp(),
            exp: exp.timestamp(),
        }
    }

    /// 检查token是否过期
    pub fn is_expired(&self) -> bool {
        let now = Utc::now().timestamp();
        now > self.exp
    }
}

/// 密码强度验证结果
#[derive(Debug, Clone)]
pub struct PasswordStrength {
    pub is_valid: bool,
    pub score: u8, // 0-100
    pub issues: Vec<String>,
}

impl PasswordStrength {
    /// 验证密码强度
    pub fn validate(password: &str) -> Self {
        let mut issues = Vec::new();
        let mut score = 0u8;

        // 长度检查
        if password.len() < 12 {
            issues.push("密码长度至少需要12位".to_string());
        } else {
            score += 25;
        }

        // 大写字母检查
        if !password.chars().any(|c| c.is_uppercase()) {
            issues.push("密码需要包含大写字母".to_string());
        } else {
            score += 20;
        }

        // 小写字母检查
        if !password.chars().any(|c| c.is_lowercase()) {
            issues.push("密码需要包含小写字母".to_string());
        } else {
            score += 20;
        }

        // 数字检查
        if !password.chars().any(|c| c.is_numeric()) {
            issues.push("密码需要包含数字".to_string());
        } else {
            score += 20;
        }

        // 特殊字符检查
        let special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
        if !password.chars().any(|c| special_chars.contains(c)) {
            issues.push("密码需要包含特殊字符".to_string());
        } else {
            score += 15;
        }

        let is_valid = issues.is_empty();

        Self {
            is_valid,
            score,
            issues,
        }
    }
}

/// 认证服务错误类型
#[derive(Debug, thiserror::Error)]
pub enum AuthError {
    #[error("Invalid credentials")]
    InvalidCredentials,

    #[error("Token expired")]
    TokenExpired,

    #[error("Invalid token")]
    InvalidToken,

    #[error("Password too weak: {0}")]
    WeakPassword(String),

    #[error("User already exists")]
    UserExists,

    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),

    #[error("Crypto error: {0}")]
    Crypto(#[from] anyhow::Error),

    #[error("JWT error: {0}")]
    Jwt(#[from] jsonwebtoken::errors::Error),
}

impl From<AuthError> for shared::AppError {
    fn from(err: AuthError) -> Self {
        match err {
            AuthError::InvalidCredentials => shared::AppError::Unauthorized("Invalid credentials".to_string()),
            AuthError::TokenExpired => shared::AppError::Unauthorized("Token expired".to_string()),
            AuthError::InvalidToken => shared::AppError::Unauthorized("Invalid token".to_string()),
            AuthError::WeakPassword(msg) => shared::AppError::BadRequest(msg),
            AuthError::UserExists => shared::AppError::Conflict("User already exists".to_string()),
            AuthError::Database(e) => shared::AppError::Database(e),
            AuthError::Crypto(e) => shared::AppError::Internal(e.to_string()),
            AuthError::Jwt(e) => shared::AppError::Unauthorized(e.to_string()),
        }
    }
}
