// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#9e52f571-3e1f-4c96-8ab2-4cbcf17f7676"
//   Timestamp: "2025-08-05T16:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "笔记模型定义完整，支持7种笔记类型的内容结构"
// }}
// 笔记相关模型和类型定义

use serde::{Deserialize, Serialize};
use serde_json::Value;

/// 笔记内容结构示例和验证

/// 登录类型笔记内容结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginContent {
    pub url: String,
    pub username: String,
    pub password: String,
    pub notes: Option<String>,
    pub totp_secret: Option<String>,
}

/// SSH密钥类型笔记内容结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SshKeyContent {
    pub key_type: String, // ed25519, rsa, ecdsa
    pub private_key: String,
    pub public_key: String,
    pub passphrase: Option<String>,
    pub host: Option<String>,
    pub username: Option<String>,
    pub port: Option<u16>,
    pub servers: Vec<String>,
}

/// API密钥类型笔记内容结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiKeyContent {
    pub service: String,
    pub api_key: String,
    pub secret: Option<String>,
    pub endpoint: Option<String>,
    pub permissions: Vec<String>,
    pub expires_at: Option<String>,
    pub headers: Option<Value>,
    pub description: Option<String>,
}

/// 证书类型笔记内容结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CertificateContent {
    pub certificate: String,
    pub private_key: String,
    pub ca_chain: Option<String>,
    pub passphrase: Option<String>,
    pub domain: String,
    pub expires_at: String,
    pub certificate_type: String, // ssl, code_signing, client
}

/// 数据库类型笔记内容结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseContent {
    pub host: String,
    pub port: u16,
    pub database: String,
    pub username: String,
    pub password: String,
    pub connection_string: Option<String>,
    pub db_type: String, // mysql, postgresql, mongodb, redis
    pub environment: String, // dev, test, prod
    pub ssl_required: bool,
}

/// TOTP类型笔记内容结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TotpContent {
    pub secret: String,
    pub issuer: String,
    pub account: String,
    pub algorithm: String, // SHA1, SHA256, SHA512
    pub digits: u32,       // 6 or 8
    pub period: u64,       // 30 seconds
    pub backup_codes: Option<Vec<String>>,
}

/// 技术笔记类型内容结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TechNoteContent {
    pub content: String,
    pub language: Option<String>, // 代码语言类型
    pub category: String,         // config, command, snippet, documentation
    pub references: Vec<String>,  // 相关链接或文档
    pub environment: Option<String>, // 适用环境
}

/// 笔记内容验证器
pub struct NoteContentValidator;

impl NoteContentValidator {
    /// 验证登录类型内容
    pub fn validate_login_content(content: &Value) -> Result<LoginContent, String> {
        serde_json::from_value(content.clone())
            .map_err(|e| format!("Invalid login content: {}", e))
    }

    /// 验证SSH密钥类型内容
    pub fn validate_ssh_key_content(content: &Value) -> Result<SshKeyContent, String> {
        serde_json::from_value(content.clone())
            .map_err(|e| format!("Invalid SSH key content: {}", e))
    }

    /// 验证API密钥类型内容
    pub fn validate_api_key_content(content: &Value) -> Result<ApiKeyContent, String> {
        serde_json::from_value(content.clone())
            .map_err(|e| format!("Invalid API key content: {}", e))
    }

    /// 验证证书类型内容
    pub fn validate_certificate_content(content: &Value) -> Result<CertificateContent, String> {
        serde_json::from_value(content.clone())
            .map_err(|e| format!("Invalid certificate content: {}", e))
    }

    /// 验证数据库类型内容
    pub fn validate_database_content(content: &Value) -> Result<DatabaseContent, String> {
        serde_json::from_value(content.clone())
            .map_err(|e| format!("Invalid database content: {}", e))
    }

    /// 验证TOTP类型内容
    pub fn validate_totp_content(content: &Value) -> Result<TotpContent, String> {
        let totp_content: TotpContent = serde_json::from_value(content.clone())
            .map_err(|e| format!("Invalid TOTP content: {}", e))?;

        // 验证必需字段
        if totp_content.secret.is_empty() {
            return Err("TOTP secret is required".to_string());
        }

        if totp_content.issuer.is_empty() {
            return Err("TOTP issuer is required".to_string());
        }

        // 验证算法
        match totp_content.algorithm.as_str() {
            "SHA1" | "SHA256" | "SHA512" => {}
            _ => return Err("Invalid TOTP algorithm".to_string()),
        }

        // 验证位数
        if totp_content.digits != 6 && totp_content.digits != 8 {
            return Err("TOTP digits must be 6 or 8".to_string());
        }

        Ok(totp_content)
    }

    /// 验证技术笔记类型内容
    pub fn validate_tech_note_content(content: &Value) -> Result<TechNoteContent, String> {
        serde_json::from_value(content.clone())
            .map_err(|e| format!("Invalid tech note content: {}", e))
    }
}
