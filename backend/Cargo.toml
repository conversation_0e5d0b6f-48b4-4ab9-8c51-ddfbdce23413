[package]
name = "securefox-backend"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[lib]
name = "securefox_backend"
path = "src/lib.rs"

[dependencies]
# Workspace dependencies
axum.workspace = true
tower.workspace = true
tower-http.workspace = true
sqlx.workspace = true
ring.workspace = true
argon2.workspace = true
zeroize.workspace = true
jsonwebtoken.workspace = true
serde.workspace = true
serde_json.workspace = true
tokio.workspace = true
uuid.workspace = true
chrono.workspace = true
anyhow.workspace = true
thiserror.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true
config.workspace = true
async-trait.workspace = true
once_cell.workspace = true

# Local dependencies
shared = { path = "../shared", features = ["axum"] }

# Additional backend-specific dependencies
axum-extra = { version = "0.9", features = ["typed-header"] }
tower-cookies = "0.10"
mime = "0.3"
base64 = "0.22"
rand = "0.8"

[dev-dependencies]
tokio-test = "0.4"
tempfile = "3.0"
