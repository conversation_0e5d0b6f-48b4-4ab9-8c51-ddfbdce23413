// {{RIPER-5: Action:"Added", Task_ID:"#8636e378", Timestamp:"2025-08-05", Role:"LD", Principle:"SOLID-S"}}
// 加密服务性能测试

#[cfg(test)]
mod performance_tests {
    use super::super::*;
    use std::time::Instant;
    use tokio::runtime::Runtime;

    /// 测试密钥派生性能 - 应该在100ms内完成
    #[test]
    fn test_key_derivation_performance() {
        let password = "test_password_with_sufficient_complexity_123!@#";
        let salt = [1u8; 16];
        
        let start = Instant::now();
        let _key = KeyDerivationService::derive_master_key(password, &salt).unwrap();
        let duration = start.elapsed();
        
        println!("Key derivation took: {:?}", duration);
        assert!(duration.as_millis() < 2000, "Key derivation took too long: {:?}", duration);
    }

    /// 测试加密性能 - 应该在100ms内完成
    #[test]
    fn test_encryption_performance() {
        let service = EncryptionService::new();
        let key = service.generate_random_bytes(32).unwrap();
        let data = "This is a test message for encryption performance testing. ".repeat(100);
        
        let start = Instant::now();
        let encrypted = service.encrypt_data(&data, &key).unwrap();
        let encryption_duration = start.elapsed();
        
        let start = Instant::now();
        let _decrypted = service.decrypt_data(&encrypted, &key).unwrap();
        let decryption_duration = start.elapsed();
        
        println!("Encryption took: {:?}", encryption_duration);
        println!("Decryption took: {:?}", decryption_duration);
        
        assert!(encryption_duration.as_millis() < 100, "Encryption took too long: {:?}", encryption_duration);
        assert!(decryption_duration.as_millis() < 100, "Decryption took too long: {:?}", decryption_duration);
    }

    /// 测试异步加密服务性能
    #[test]
    fn test_async_crypto_service_performance() {
        let rt = Runtime::new().unwrap();
        
        rt.block_on(async {
            let service = DefaultCryptoService::new();
            let password = "test_password_123!";
            let salt = service.generate_salt().await.unwrap();
            
            // 测试异步密钥派生性能
            let start = Instant::now();
            let master_key = service.derive_master_key(password, &salt).await.unwrap();
            let key_derivation_duration = start.elapsed();
            
            // 测试异步加密性能
            let data = "Test data for async encryption";
            let start = Instant::now();
            let encrypted = service.encrypt_data(data, master_key.as_bytes()).await.unwrap();
            let encryption_duration = start.elapsed();
            
            let start = Instant::now();
            let _decrypted = service.decrypt_data(&encrypted, master_key.as_bytes()).await.unwrap();
            let decryption_duration = start.elapsed();
            
            println!("Async key derivation took: {:?}", key_derivation_duration);
            println!("Async encryption took: {:?}", encryption_duration);
            println!("Async decryption took: {:?}", decryption_duration);
            
            assert!(key_derivation_duration.as_millis() < 2000);
            assert!(encryption_duration.as_millis() < 100);
            assert!(decryption_duration.as_millis() < 100);
        });
    }

    /// 测试大量数据加密性能
    #[test]
    fn test_large_data_encryption_performance() {
        let service = EncryptionService::new();
        let key = service.generate_random_bytes(32).unwrap();
        
        // 测试1KB数据
        let data_1kb = "x".repeat(1024);
        let start = Instant::now();
        let encrypted = service.encrypt_data(&data_1kb, &key).unwrap();
        let _decrypted = service.decrypt_data(&encrypted, &key).unwrap();
        let duration_1kb = start.elapsed();
        
        // 测试10KB数据
        let data_10kb = "x".repeat(10240);
        let start = Instant::now();
        let encrypted = service.encrypt_data(&data_10kb, &key).unwrap();
        let _decrypted = service.decrypt_data(&encrypted, &key).unwrap();
        let duration_10kb = start.elapsed();
        
        println!("1KB data encryption/decryption took: {:?}", duration_1kb);
        println!("10KB data encryption/decryption took: {:?}", duration_10kb);
        
        assert!(duration_1kb.as_millis() < 50);
        assert!(duration_10kb.as_millis() < 200);
    }

    /// 测试并发加密性能
    #[test]
    fn test_concurrent_encryption_performance() {
        let rt = Runtime::new().unwrap();
        
        rt.block_on(async {
            let service = std::sync::Arc::new(DefaultCryptoService::new());
            let key = service.generate_random_bytes(32).await.unwrap();
            let data = "Concurrent encryption test data";
            
            let start = Instant::now();
            
            // 并发执行10个加密操作
            let mut handles = Vec::new();
            for i in 0..10 {
                let service_clone = service.clone();
                let key_clone = key.clone();
                let data_with_id = format!("{} - {}", data, i);
                
                let handle = tokio::spawn(async move {
                    let encrypted = service_clone.encrypt_data(&data_with_id, &key_clone).await.unwrap();
                    service_clone.decrypt_data(&encrypted, &key_clone).await.unwrap()
                });
                
                handles.push(handle);
            }
            
            // 等待所有操作完成
            for handle in handles {
                handle.await.unwrap();
            }
            
            let duration = start.elapsed();
            println!("10 concurrent encryption/decryption operations took: {:?}", duration);
            
            assert!(duration.as_millis() < 1000);
        });
    }
}
