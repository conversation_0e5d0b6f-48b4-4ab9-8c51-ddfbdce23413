# 安全狐 MVP 需求文档
**定位**: 面向个人开发者/技术人员的密钥和凭据管理工具

## 核心用户画像
**目标用户**: 个人开发者、系统管理员、技术人员  
**核心诉求**: 管理密码、SSH密钥、API密钥、证书、TOTP等技术资源  
**使用场景**: 日常开发工作、服务器管理、API集成、安全认证

## MVP功能拆解

### Phase 1: 核心技术功能 (4-5周)

#### 1. 用户认证系统
**功能描述**: 基于主密码的零知识加密认证
- 用户注册（邮箱 + 主密码）
- 用户登录（主密码验证）
- 密钥派生（PBKDF2-SHA256）
- 会话管理（JWT token）
- 自动锁定和解锁

**验收标准**:
- 主密码强度验证（最少12位，包含大小写、数字、特殊字符）
- 密钥派生时间 < 1秒
- 会话超时自动锁定
- 零知识架构验证（服务端无法访问用户数据）

#### 2. 密码管理
**功能描述**: 网站登录凭据的存储和自动填充
- 密码条目CRUD操作
- 网站URL匹配算法
- 浏览器自动填充
- 密码捕获和保存提示

**数据结构**:
```rust
struct LoginItem {
    name: String,
    url: String,
    username: String,
    password: String,
    notes: Option<String>,
    tags: Vec<String>,
}
```

**验收标准**:
- 支持主流网站的自动填充
- URL匹配准确率 > 95%
- 密码捕获成功率 > 90%
- 加密存储验证

#### 3. SSH密钥管理
**功能描述**: SSH密钥对的安全存储和管理
- SSH私钥加密存储
- 公钥管理和复制
- SSH Agent集成（可选）
- 密钥生成功能

**数据结构**:
```rust
struct SshKeyItem {
    name: String,
    key_type: String,        // rsa, ed25519, ecdsa
    private_key: String,     // 加密存储的私钥
    public_key: String,      // 公钥
    passphrase: Option<String>,
    comment: Option<String>,
    servers: Vec<String>,    // 关联的服务器
}
```

**验收标准**:
- 支持RSA、Ed25519、ECDSA密钥类型
- 私钥加密存储安全性验证
- 公钥一键复制功能
- 密钥生成符合安全标准

#### 4. API密钥管理
**功能描述**: 各种服务API密钥的安全存储
- API密钥加密存储
- 服务分类管理
- 一键复制功能
- 到期时间提醒

**数据结构**:
```rust
struct ApiKeyItem {
    name: String,
    service: String,         // GitHub, AWS, Google Cloud等
    api_key: String,         // 加密存储的API密钥
    secret_key: Option<String>,
    endpoint: Option<String>,
    expires_at: Option<DateTime>,
    permissions: Vec<String>,
    notes: Option<String>,
}
```

**验收标准**:
- 支持主流服务（GitHub, AWS, Google Cloud, Docker Hub等）
- 密钥一键复制到剪贴板
- 到期提醒功能
- 权限和范围记录

#### 5. TOTP生成器
**功能描述**: 两步验证码的生成和管理
- TOTP种子安全存储
- 实时验证码生成
- 倒计时显示
- 备份码管理

**数据结构**:
```rust
struct TotpItem {
    name: String,
    service: String,
    secret: String,          // 加密存储的TOTP种子
    algorithm: String,       // SHA1, SHA256, SHA512
    digits: u8,              // 通常是6位
    period: u32,             // 通常是30秒
    backup_codes: Vec<String>,
}
```

**验收标准**:
- 验证码生成准确性100%
- 倒计时显示精确
- 支持不同算法和位数
- 备份码安全存储

#### 6. 基础浏览器扩展
**功能描述**: Chrome/Firefox扩展的基础功能
- 弹窗界面（登录、条目列表、详情）
- 自动填充检测和执行
- 密码捕获提示
- 快速搜索功能

**UI组件**:
- 登录界面
- 条目列表（按类型分组）
- 条目详情页面
- 搜索和过滤
- TOTP生成器界面

**验收标准**:
- 扩展安装和激活成功
- 主流网站自动填充测试通过
- UI响应时间 < 200ms
- 跨浏览器兼容性测试

### Phase 2: 扩展技术功能 (3-4周)

#### 7. 证书管理
**功能描述**: SSL证书和代码签名证书的存储管理
- 证书文件加密存储
- 证书信息解析显示
- 到期监控和提醒
- 证书链验证

**数据结构**:
```rust
struct CertificateItem {
    name: String,
    cert_type: String,       // SSL, Code Signing, Client
    certificate: String,     // 证书内容
    private_key: Option<String>,
    chain: Vec<String>,      // 证书链
    expires_at: DateTime,
    issuer: String,
    subject: String,
    domains: Vec<String>,    // SSL证书的域名
}
```

#### 8. 数据库凭据管理
**功能描述**: 数据库连接信息的安全存储
- 连接字符串加密存储
- 数据库类型分类
- 连接测试功能
- 环境区分（开发、测试、生产）

**数据结构**:
```rust
struct DatabaseItem {
    name: String,
    db_type: String,         // MySQL, PostgreSQL, MongoDB等
    host: String,
    port: u16,
    database: String,
    username: String,
    password: String,
    connection_string: Option<String>,
    environment: String,     // dev, test, prod
    ssl_required: bool,
}
```

#### 9. 技术笔记（简化版）
**功能描述**: 技术配置和命令的快速记录
- 纯文本笔记存储
- 代码语法高亮
- 标签分类
- 快速搜索

**数据结构**:
```rust
struct TechNoteItem {
    name: String,
    content: String,
    language: Option<String>, // 代码语言类型
    tags: Vec<String>,
    category: String,         // config, command, snippet等
}
```

#### 10. 密码/密钥生成器
**功能描述**: 强密码和API密钥的生成工具
- 可配置密码生成（长度、字符集）
- API密钥格式生成
- 密码强度评估
- 生成历史记录

#### 11. CLI工具（可选）
**功能描述**: 命令行访问和环境变量注入
- 命令行登录认证
- 密钥查询和复制
- 环境变量导出
- 脚本集成支持

### Phase 3: 高级功能 (按需开发)

#### 12. 云端同步
- 多设备数据同步
- 冲突解决机制
- 离线模式支持

#### 13. 团队共享
- 密钥安全共享
- 权限管理
- 审计日志

#### 14. 高级集成
- SSH Agent深度集成
- IDE插件支持
- CI/CD工具集成

## 技术实现要点

### 安全要求
- 所有敏感数据客户端加密（AES-256-GCM）
- 零知识架构（服务端无法解密用户数据）
- 密钥派生使用PBKDF2-SHA256（100,000次迭代）
- 内存安全（Rust语言特性）

### 性能要求
- 加解密操作 < 100ms
- 搜索响应时间 < 200ms
- 扩展启动时间 < 500ms
- 数据库查询优化

### 兼容性要求
- Chrome 88+, Firefox 85+
- macOS 10.15+, Windows 10+, Linux
- 响应式UI设计

## 开发里程碑

### 里程碑1 (2周): 核心基础
- 用户认证系统
- 基础数据模型
- 加密服务实现

### 里程碑2 (2周): 密码管理
- 密码条目CRUD
- 浏览器扩展基础框架
- 自动填充功能

### 里程碑3 (1周): SSH和API密钥
- SSH密钥管理
- API密钥管理
- 密钥生成器

### 里程碑4 (1周): TOTP和完善
- TOTP生成器
- UI优化
- 测试和调试

这个MVP设计专注于个人技术用户的核心需求，避免了过度复杂的功能，确保快速交付和实用性。
