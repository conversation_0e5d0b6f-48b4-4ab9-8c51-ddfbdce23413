{"rustc": 5148484765029645790, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 3033921117576893, "path": 6957532974620942060, "deps": [[966925859616469517, "ahash", false, 13774734588598400597], [9150530836556604396, "allocator_api2", false, 1351160875477959618]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-b91782daa9740288/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}