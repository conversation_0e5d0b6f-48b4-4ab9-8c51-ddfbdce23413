// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#a194fea0-d065-4b72-85be-8e349bb0f85e"
//   Timestamp: "2025-08-05T16:40:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "类型定义完整，支持7种笔记类型和扩展通信"
// }}
// 扩展全局类型定义

export interface User {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  isLoading: boolean;
}

export interface LoginRequest {
  email: string;
  master_password: string;
}

export interface RegisterRequest {
  email: string;
  master_password: string;
}

export interface AuthResponse {
  success: boolean;
  data?: {
    user: User;
    token: string;
  };
  error?: string;
}

// 笔记类型枚举
export enum NoteType {
  Login = 'login',
  SshKey = 'ssh_key',
  ApiKey = 'api_key',
  Certificate = 'certificate',
  Database = 'database',
  Totp = 'totp',
  TechNote = 'tech_note',
}

// 笔记内容接口
export interface LoginContent {
  url: string;
  username: string;
  password: string;
  notes?: string;
  totp_secret?: string;
}

export interface SshKeyContent {
  key_type: string;
  private_key: string;
  public_key: string;
  passphrase?: string;
  host?: string;
  username?: string;
  port?: number;
  servers: string[];
}

export interface ApiKeyContent {
  service: string;
  api_key: string;
  secret?: string;
  endpoint?: string;
  permissions: string[];
  expires_at?: string;
  headers?: Record<string, any>;
  description?: string;
}

export interface CertificateContent {
  certificate: string;
  private_key: string;
  ca_chain?: string;
  passphrase?: string;
  domain: string;
  expires_at: string;
  certificate_type: string;
}

export interface DatabaseContent {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  connection_string?: string;
  db_type: string;
  environment: string;
  ssl_required: boolean;
}

export interface TotpContent {
  secret: string;
  issuer: string;
  account: string;
  algorithm: string;
  digits: number;
  period: number;
  backup_codes?: string[];
}

export interface TechNoteContent {
  content: string;
  language?: string;
  category: string;
  references: string[];
  environment?: string;
}

export type NoteContent = 
  | LoginContent 
  | SshKeyContent 
  | ApiKeyContent 
  | CertificateContent 
  | DatabaseContent 
  | TotpContent 
  | TechNoteContent;

export interface SecureNote {
  id: string;
  user_id: string;
  title: string;
  note_type: NoteType;
  content: NoteContent;
  tags: string[];
  is_favorite: boolean;
  template_id?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateNoteRequest {
  title: string;
  note_type: NoteType;
  content: NoteContent;
  tags: string[];
  is_favorite: boolean;
  template_id?: string;
}

export interface UpdateNoteRequest {
  title?: string;
  content?: NoteContent;
  tags?: string[];
  is_favorite?: boolean;
}

export interface TotpResponse {
  code: string;
  remaining_seconds: number;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// 扩展消息类型
export enum MessageType {
  // 认证相关
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  REGISTER = 'REGISTER',
  VERIFY_TOKEN = 'VERIFY_TOKEN',
  
  // 笔记管理
  GET_NOTES = 'GET_NOTES',
  CREATE_NOTE = 'CREATE_NOTE',
  UPDATE_NOTE = 'UPDATE_NOTE',
  DELETE_NOTE = 'DELETE_NOTE',
  SEARCH_NOTES = 'SEARCH_NOTES',
  
  // TOTP相关
  GENERATE_TOTP = 'GENERATE_TOTP',
  
  // 自动填充相关
  DETECT_FORMS = 'DETECT_FORMS',
  FILL_FORM = 'FILL_FORM',
  CAPTURE_CREDENTIALS = 'CAPTURE_CREDENTIALS',
  
  // 通用
  PING = 'PING',
  GET_ACTIVE_TAB = 'GET_ACTIVE_TAB',
  SHOW_NOTIFICATION = 'SHOW_NOTIFICATION',
}

export interface ExtensionMessage<T = any> {
  type: MessageType;
  payload?: T;
  requestId?: string;
}

export interface MessageResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  requestId?: string;
}

// 表单检测相关
export interface FormField {
  element: HTMLInputElement;
  type: 'username' | 'password' | 'email';
  selector: string;
}

export interface DetectedForm {
  url: string;
  title: string;
  fields: FormField[];
  submitButton?: HTMLButtonElement;
}

// 存储相关
export interface StorageData {
  auth?: AuthState;
  settings?: ExtensionSettings;
  cache?: {
    notes?: SecureNote[];
    lastSync?: string;
  };
}

export interface ExtensionSettings {
  theme: 'light' | 'dark' | 'auto';
  autoLock: boolean;
  autoLockTimeout: number; // 分钟
  autoFill: boolean;
  showNotifications: boolean;
  apiEndpoint: string;
}

// 错误类型
export class ExtensionError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ExtensionError';
  }
}

// 工具类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
