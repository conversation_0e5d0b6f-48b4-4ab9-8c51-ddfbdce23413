use async_trait::async_trait;
use sqlx::{<PERSON>, Sqlite, SqlitePool};
use uuid::Uuid;

use super::models::*;
use shared::{AppError, AppResult};

/// 用户仓库trait
#[async_trait]
pub trait UserRepository: Send + Sync {
    async fn create_user(&self, request: CreateUserRequest) -> AppResult<DbUser>;
    async fn find_user_by_email(&self, email: &str) -> AppResult<Option<DbUser>>;
    async fn find_user_by_id(&self, id: &str) -> AppResult<Option<DbUser>>;
}

/// 笔记仓库trait
#[async_trait]
pub trait NoteRepository: Send + Sync {
    async fn create_note(&self, request: CreateNoteRequest) -> AppResult<DbSecureNote>;
    async fn find_notes_by_user(&self, user_id: &str) -> AppR<PERSON>ult<Vec<DbSecureNote>>;
    async fn find_note_by_id(&self, id: &str) -> AppR<PERSON>ult<Option<DbSecureNote>>;
    async fn update_note(&self, id: &str, request: UpdateNoteRequest) -> AppResult<DbSecureNote>;
    async fn delete_note(&self, id: &str) -> AppResult<()>;
    async fn find_notes_by_type(&self, user_id: &str, note_type: &str) -> AppResult<Vec<DbSecureNote>>;
}

/// 模板仓库trait
#[async_trait]
pub trait TemplateRepository: Send + Sync {
    async fn find_all_templates(&self) -> AppResult<Vec<DbNoteTemplate>>;
    async fn find_template_by_id(&self, id: &str) -> AppResult<Option<DbNoteTemplate>>;
    async fn find_templates_by_category(&self, category: &str) -> AppResult<Vec<DbNoteTemplate>>;
}

/// SQLite用户仓库实现
pub struct SqliteUserRepository {
    pool: Pool<Sqlite>,
}

impl SqliteUserRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserRepository for SqliteUserRepository {
    async fn create_user(&self, request: CreateUserRequest) -> AppResult<DbUser> {
        let id = Uuid::new_v4().to_string();
        let now = chrono::Utc::now();

        let user = sqlx::query_as::<_, DbUser>(
            r#"
            INSERT INTO users (id, email, password_hash, salt, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
        )
        .bind(&id)
        .bind(&request.email)
        .bind(&request.password_hash)
        .bind(&request.salt)
        .bind(now)
        .bind(now)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| match e {
            sqlx::Error::Database(db_err) if db_err.is_unique_violation() => {
                AppError::Conflict("Email already exists".to_string())
            }
            _ => AppError::Database(e),
        })?;

        Ok(user)
    }

    async fn find_user_by_email(&self, email: &str) -> AppResult<Option<DbUser>> {
        let user = sqlx::query_as::<_, DbUser>("SELECT * FROM users WHERE email = ?")
            .bind(email)
            .fetch_optional(&self.pool)
            .await?;

        Ok(user)
    }

    async fn find_user_by_id(&self, id: &str) -> AppResult<Option<DbUser>> {
        let user = sqlx::query_as::<_, DbUser>("SELECT * FROM users WHERE id = ?")
            .bind(id)
            .fetch_optional(&self.pool)
            .await?;

        Ok(user)
    }
}

/// SQLite笔记仓库实现
pub struct SqliteNoteRepository {
    pool: Pool<Sqlite>,
}

impl SqliteNoteRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl NoteRepository for SqliteNoteRepository {
    async fn create_note(&self, request: CreateNoteRequest) -> AppResult<DbSecureNote> {
        let id = Uuid::new_v4().to_string();
        let now = chrono::Utc::now();
        let is_favorite = request.is_favorite.unwrap_or(false);

        let note = sqlx::query_as::<_, DbSecureNote>(
            r#"
            INSERT INTO secure_notes (id, user_id, title, note_type, content, tags, is_favorite, template_id, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
        )
        .bind(&id)
        .bind(&request.user_id)
        .bind(&request.title)
        .bind(&request.note_type)
        .bind(&request.content)
        .bind(&request.tags)
        .bind(is_favorite)
        .bind(&request.template_id)
        .bind(now)
        .bind(now)
        .fetch_one(&self.pool)
        .await?;

        Ok(note)
    }

    async fn find_notes_by_user(&self, user_id: &str) -> AppResult<Vec<DbSecureNote>> {
        let notes = sqlx::query_as::<_, DbSecureNote>(
            "SELECT * FROM secure_notes WHERE user_id = ? ORDER BY created_at DESC"
        )
        .bind(user_id)
        .fetch_all(&self.pool)
        .await?;

        Ok(notes)
    }

    async fn find_note_by_id(&self, id: &str) -> AppResult<Option<DbSecureNote>> {
        let note = sqlx::query_as::<_, DbSecureNote>("SELECT * FROM secure_notes WHERE id = ?")
            .bind(id)
            .fetch_optional(&self.pool)
            .await?;

        Ok(note)
    }

    async fn update_note(&self, id: &str, request: UpdateNoteRequest) -> AppResult<DbSecureNote> {
        // 构建动态更新查询
        let mut query = "UPDATE secure_notes SET updated_at = CURRENT_TIMESTAMP".to_string();
        let mut params: Vec<String> = Vec::new();

        if let Some(title) = &request.title {
            query.push_str(", title = ?");
            params.push(title.clone());
        }
        if let Some(content) = &request.content {
            query.push_str(", content = ?");
            params.push(content.clone());
        }
        if let Some(tags) = &request.tags {
            query.push_str(", tags = ?");
            params.push(tags.clone());
        }
        if let Some(is_favorite) = request.is_favorite {
            query.push_str(", is_favorite = ?");
            params.push(is_favorite.to_string());
        }

        query.push_str(" WHERE id = ? RETURNING *");
        params.push(id.to_string());

        // 执行更新查询
        let mut query_builder = sqlx::query_as::<_, DbSecureNote>(&query);
        for param in &params[..params.len()-1] {
            if param == "true" || param == "false" {
                query_builder = query_builder.bind(param.parse::<bool>().unwrap_or(false));
            } else {
                query_builder = query_builder.bind(param);
            }
        }
        query_builder = query_builder.bind(id);

        let note = query_builder
            .fetch_one(&self.pool)
            .await
            .map_err(|e| match e {
                sqlx::Error::RowNotFound => AppError::NotFound("Note not found".to_string()),
                _ => AppError::Database(e),
            })?;

        Ok(note)
    }

    async fn delete_note(&self, id: &str) -> AppResult<()> {
        let result = sqlx::query("DELETE FROM secure_notes WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound("Note not found".to_string()));
        }

        Ok(())
    }

    async fn find_notes_by_type(&self, user_id: &str, note_type: &str) -> AppResult<Vec<DbSecureNote>> {
        let notes = sqlx::query_as::<_, DbSecureNote>(
            "SELECT * FROM secure_notes WHERE user_id = ? AND note_type = ? ORDER BY created_at DESC"
        )
        .bind(user_id)
        .bind(note_type)
        .fetch_all(&self.pool)
        .await?;

        Ok(notes)
    }
}

/// SQLite模板仓库实现
pub struct SqliteTemplateRepository {
    pool: Pool<Sqlite>,
}

impl SqliteTemplateRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl TemplateRepository for SqliteTemplateRepository {
    async fn find_all_templates(&self) -> AppResult<Vec<DbNoteTemplate>> {
        let templates = sqlx::query_as::<_, DbNoteTemplate>(
            "SELECT * FROM note_templates ORDER BY category, name"
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(templates)
    }

    async fn find_template_by_id(&self, id: &str) -> AppResult<Option<DbNoteTemplate>> {
        let template = sqlx::query_as::<_, DbNoteTemplate>("SELECT * FROM note_templates WHERE id = ?")
            .bind(id)
            .fetch_optional(&self.pool)
            .await?;

        Ok(template)
    }

    async fn find_templates_by_category(&self, category: &str) -> AppResult<Vec<DbNoteTemplate>> {
        let templates = sqlx::query_as::<_, DbNoteTemplate>(
            "SELECT * FROM note_templates WHERE category = ? ORDER BY name"
        )
        .bind(category)
        .fetch_all(&self.pool)
        .await?;

        Ok(templates)
    }
}
