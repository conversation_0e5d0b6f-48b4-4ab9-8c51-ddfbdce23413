// {{RIPER-5: Action:"Added", Task_ID:"#8636e378", Timestamp:"2025-08-05", Role:"LD", Principle:"SOLID-S"}}
// AES-256-GCM 加解密实现

use ring::{aead, rand::{SecureRandom, SystemRandom}};
use anyhow::{Result, anyhow};
use base64::{Engine as _, engine::general_purpose};
use serde::{Serialize, Deserialize};
use zeroize::{Zeroize, ZeroizeOnDrop};

/// 加密数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptedData {
    /// Base64编码的加密数据
    pub ciphertext: String,
    /// Base64编码的随机数
    pub nonce: String,
}

/// AES-256-GCM 加密服务
pub struct EncryptionService {
    rng: SystemRandom,
}

impl Default for EncryptionService {
    fn default() -> Self {
        Self::new()
    }
}

impl EncryptionService {
    /// 创建新的加密服务实例
    pub fn new() -> Self {
        Self {
            rng: SystemRandom::new(),
        }
    }

    /// 加密数据
    ///
    /// # 参数
    /// * `data` - 要加密的明文数据
    /// * `key` - 32字节的加密密钥
    ///
    /// # 返回
    /// * `Result<EncryptedData>` - 加密后的数据结构
    pub fn encrypt_data(&self, data: &str, key: &[u8]) -> Result<EncryptedData> {
        if key.len() != 32 {
            return Err(anyhow!("Invalid key length: expected 32 bytes, got {}", key.len()));
        }

        // 创建加密密钥
        let unbound_key = aead::UnboundKey::new(&aead::AES_256_GCM, key)
            .map_err(|_| anyhow!("Failed to create encryption key"))?;
        let sealing_key = aead::LessSafeKey::new(unbound_key);

        // 生成随机nonce (12字节用于AES-GCM)
        let mut nonce_bytes = [0u8; 12];
        self.rng.fill(&mut nonce_bytes)
            .map_err(|_| anyhow!("Failed to generate nonce"))?;

        let nonce = aead::Nonce::assume_unique_for_key(nonce_bytes);

        // 加密数据
        let mut in_out = data.as_bytes().to_vec();
        sealing_key.seal_in_place_append_tag(nonce, aead::Aad::empty(), &mut in_out)
            .map_err(|_| anyhow!("Failed to encrypt data"))?;

        // 编码为Base64
        let ciphertext = general_purpose::STANDARD.encode(&in_out);
        let nonce_b64 = general_purpose::STANDARD.encode(&nonce_bytes);

        Ok(EncryptedData {
            ciphertext,
            nonce: nonce_b64,
        })
    }

    /// 解密数据
    ///
    /// # 参数
    /// * `encrypted_data` - 加密的数据结构
    /// * `key` - 32字节的解密密钥
    ///
    /// # 返回
    /// * `Result<String>` - 解密后的明文数据
    pub fn decrypt_data(&self, encrypted_data: &EncryptedData, key: &[u8]) -> Result<String> {
        if key.len() != 32 {
            return Err(anyhow!("Invalid key length: expected 32 bytes, got {}", key.len()));
        }

        // 解码Base64数据
        let mut ciphertext = general_purpose::STANDARD.decode(&encrypted_data.ciphertext)
            .map_err(|_| anyhow!("Failed to decode ciphertext"))?;
        let nonce_bytes = general_purpose::STANDARD.decode(&encrypted_data.nonce)
            .map_err(|_| anyhow!("Failed to decode nonce"))?;

        if nonce_bytes.len() != 12 {
            return Err(anyhow!("Invalid nonce length: expected 12 bytes, got {}", nonce_bytes.len()));
        }

        // 创建解密密钥
        let unbound_key = aead::UnboundKey::new(&aead::AES_256_GCM, key)
            .map_err(|_| anyhow!("Failed to create decryption key"))?;
        let opening_key = aead::LessSafeKey::new(unbound_key);

        // 创建nonce
        let mut nonce_array = [0u8; 12];
        nonce_array.copy_from_slice(&nonce_bytes);
        let nonce = aead::Nonce::assume_unique_for_key(nonce_array);

        // 解密数据
        let plaintext = opening_key.open_in_place(nonce, aead::Aad::empty(), &mut ciphertext)
            .map_err(|_| anyhow!("Failed to decrypt data"))?;

        // 转换为字符串
        String::from_utf8(plaintext.to_vec())
            .map_err(|_| anyhow!("Failed to convert decrypted data to string"))
    }

    /// 生成安全随机数
    ///
    /// # 参数
    /// * `length` - 随机数长度
    ///
    /// # 返回
    /// * `Result<Vec<u8>>` - 生成的随机数
    pub fn generate_random_bytes(&self, length: usize) -> Result<Vec<u8>> {
        let mut bytes = vec![0u8; length];
        self.rng.fill(&mut bytes)
            .map_err(|_| anyhow!("Failed to generate random bytes"))?;
        Ok(bytes)
    }

    /// 生成安全的盐值
    ///
    /// # 返回
    /// * `Result<[u8; 16]>` - 16字节的盐值
    pub fn generate_salt(&self) -> Result<[u8; 16]> {
        let mut salt = [0u8; 16];
        self.rng.fill(&mut salt)
            .map_err(|_| anyhow!("Failed to generate salt"))?;
        Ok(salt)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encrypt_decrypt_data() {
        let service = EncryptionService::new();
        let key = service.generate_random_bytes(32).unwrap();
        let plaintext = "Hello, SecureFox!";

        // 加密数据
        let encrypted = service.encrypt_data(plaintext, &key).unwrap();

        // 验证加密数据结构
        assert!(!encrypted.ciphertext.is_empty());
        assert!(!encrypted.nonce.is_empty());

        // 解密数据
        let decrypted = service.decrypt_data(&encrypted, &key).unwrap();
        assert_eq!(decrypted, plaintext);
    }

    #[test]
    fn test_encrypt_different_nonces() {
        let service = EncryptionService::new();
        let key = service.generate_random_bytes(32).unwrap();
        let plaintext = "Test data";

        // 多次加密相同数据应该产生不同的密文（因为nonce不同）
        let encrypted1 = service.encrypt_data(plaintext, &key).unwrap();
        let encrypted2 = service.encrypt_data(plaintext, &key).unwrap();

        assert_ne!(encrypted1.ciphertext, encrypted2.ciphertext);
        assert_ne!(encrypted1.nonce, encrypted2.nonce);

        // 但解密结果应该相同
        let decrypted1 = service.decrypt_data(&encrypted1, &key).unwrap();
        let decrypted2 = service.decrypt_data(&encrypted2, &key).unwrap();
        assert_eq!(decrypted1, plaintext);
        assert_eq!(decrypted2, plaintext);
    }

    #[test]
    fn test_invalid_key_length() {
        let service = EncryptionService::new();
        let short_key = vec![0u8; 16]; // 只有16字节，应该是32字节
        let plaintext = "Test data";

        let result = service.encrypt_data(plaintext, &short_key);
        assert!(result.is_err());
    }

    #[test]
    fn test_generate_random_bytes() {
        let service = EncryptionService::new();

        let bytes1 = service.generate_random_bytes(32).unwrap();
        let bytes2 = service.generate_random_bytes(32).unwrap();

        assert_eq!(bytes1.len(), 32);
        assert_eq!(bytes2.len(), 32);
        assert_ne!(bytes1, bytes2); // 应该生成不同的随机数
    }

    #[test]
    fn test_generate_salt() {
        let service = EncryptionService::new();

        let salt1 = service.generate_salt().unwrap();
        let salt2 = service.generate_salt().unwrap();

        assert_eq!(salt1.len(), 16);
        assert_eq!(salt2.len(), 16);
        assert_ne!(salt1, salt2); // 应该生成不同的盐值
    }
}
