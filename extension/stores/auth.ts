// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#a194fea0-d065-4b72-85be-8e349bb0f85e"
//   Timestamp: "2025-08-05T16:40:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "认证状态管理完整，支持登录、注册、自动锁定"
// }}
// 认证状态管理

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { User, AuthState, LoginRequest, RegisterRequest } from '../types';
import { apiClient } from '../utils/api';
import { AuthStorage, ActivityStorage, SettingsStorage } from '../utils/storage';
import { showErrorNotification, showSuccessNotification } from '../utils/messaging';

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const isAuthenticated = ref(false);
  const user = ref<User | null>(null);
  const token = ref<string | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // 计算属性
  const isLoggedIn = computed(() => isAuthenticated.value && !!token.value);
  const userEmail = computed(() => user.value?.email || '');

  // 初始化认证状态
  async function initialize(): Promise<void> {
    try {
      isLoading.value = true;
      error.value = null;

      // 从存储中恢复认证状态
      const authState = await AuthStorage.getAuthState();
      
      if (authState?.isAuthenticated && authState.token) {
        // 检查是否需要自动锁定
        const shouldLock = await ActivityStorage.shouldAutoLock();
        
        if (shouldLock) {
          await logout();
          return;
        }

        // 恢复状态
        isAuthenticated.value = authState.isAuthenticated;
        user.value = authState.user;
        token.value = authState.token;
        
        // 设置API客户端token
        apiClient.setToken(authState.token);

        // 验证token是否仍然有效
        const verifyResponse = await apiClient.verifyToken();
        
        if (!verifyResponse.success) {
          // Token无效，清除认证状态
          await logout();
        } else {
          // 更新最后活动时间
          await ActivityStorage.updateLastActivity();
        }
      }
    } catch (err) {
      console.error('Failed to initialize auth state:', err);
      error.value = 'Failed to initialize authentication';
      await logout();
    } finally {
      isLoading.value = false;
    }
  }

  // 登录
  async function login(credentials: LoginRequest): Promise<boolean> {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await apiClient.login(credentials);

      if (response.success && response.data) {
        // 更新状态
        isAuthenticated.value = true;
        user.value = response.data.user;
        token.value = response.data.token;

        // 保存到存储
        const authState: AuthState = {
          isAuthenticated: true,
          user: response.data.user,
          token: response.data.token,
          isLoading: false,
        };
        
        await AuthStorage.setAuthState(authState);
        await ActivityStorage.updateLastActivity();

        await showSuccessNotification('登录成功');
        return true;
      } else {
        error.value = response.error || 'Login failed';
        await showErrorNotification(error.value);
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed';
      error.value = errorMessage;
      await showErrorNotification(errorMessage);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // 注册
  async function register(userData: RegisterRequest): Promise<boolean> {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await apiClient.register(userData);

      if (response.success && response.data) {
        // 更新状态
        isAuthenticated.value = true;
        user.value = response.data.user;
        token.value = response.data.token;

        // 保存到存储
        const authState: AuthState = {
          isAuthenticated: true,
          user: response.data.user,
          token: response.data.token,
          isLoading: false,
        };
        
        await AuthStorage.setAuthState(authState);
        await ActivityStorage.updateLastActivity();

        await showSuccessNotification('注册成功');
        return true;
      } else {
        error.value = response.error || 'Registration failed';
        await showErrorNotification(error.value);
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Registration failed';
      error.value = errorMessage;
      await showErrorNotification(errorMessage);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // 登出
  async function logout(): Promise<void> {
    try {
      isLoading.value = true;

      // 如果有token，尝试调用登出API
      if (token.value) {
        await apiClient.logout();
      }
    } catch (err) {
      console.error('Logout API call failed:', err);
    } finally {
      // 无论API调用是否成功，都清除本地状态
      isAuthenticated.value = false;
      user.value = null;
      token.value = null;
      error.value = null;
      isLoading.value = false;

      // 清除存储
      await AuthStorage.clearAuthState();
      
      // 清除API客户端token
      apiClient.setToken(null);
    }
  }

  // 更新最后活动时间
  async function updateActivity(): Promise<void> {
    if (isAuthenticated.value) {
      await ActivityStorage.updateLastActivity();
    }
  }

  // 检查自动锁定
  async function checkAutoLock(): Promise<boolean> {
    if (!isAuthenticated.value) {
      return false;
    }

    const shouldLock = await ActivityStorage.shouldAutoLock();
    
    if (shouldLock) {
      await logout();
      await showErrorNotification('会话已过期，请重新登录');
      return true;
    }

    return false;
  }

  // 验证密码强度
  function validatePassword(password: string): { isValid: boolean; message: string } {
    if (password.length < 8) {
      return { isValid: false, message: '密码长度至少8位' };
    }

    if (!/[A-Z]/.test(password)) {
      return { isValid: false, message: '密码必须包含大写字母' };
    }

    if (!/[a-z]/.test(password)) {
      return { isValid: false, message: '密码必须包含小写字母' };
    }

    if (!/[0-9]/.test(password)) {
      return { isValid: false, message: '密码必须包含数字' };
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return { isValid: false, message: '密码必须包含特殊字符' };
    }

    return { isValid: true, message: '密码强度良好' };
  }

  // 验证邮箱格式
  function validateEmail(email: string): { isValid: boolean; message: string } {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!emailRegex.test(email)) {
      return { isValid: false, message: '邮箱格式不正确' };
    }

    return { isValid: true, message: '邮箱格式正确' };
  }

  // 清除错误
  function clearError(): void {
    error.value = null;
  }

  return {
    // 状态
    isAuthenticated,
    user,
    token,
    isLoading,
    error,
    
    // 计算属性
    isLoggedIn,
    userEmail,
    
    // 方法
    initialize,
    login,
    register,
    logout,
    updateActivity,
    checkAutoLock,
    validatePassword,
    validateEmail,
    clearError,
  };
});

// 设置自动活动更新
export function setupActivityTracking(): void {
  const authStore = useAuthStore();

  // 监听用户活动
  const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
  
  let activityTimeout: number | null = null;
  
  const updateActivity = () => {
    if (activityTimeout) {
      clearTimeout(activityTimeout);
    }
    
    activityTimeout = window.setTimeout(() => {
      authStore.updateActivity();
    }, 1000); // 防抖1秒
  };

  events.forEach(event => {
    document.addEventListener(event, updateActivity, true);
  });

  // 定期检查自动锁定
  setInterval(() => {
    authStore.checkAutoLock();
  }, 60000); // 每分钟检查一次
}
