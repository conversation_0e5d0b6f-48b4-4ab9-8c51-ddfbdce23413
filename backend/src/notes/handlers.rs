// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#9e52f571-3e1f-4c96-8ab2-4cbcf17f7676"
//   Timestamp: "2025-08-05T16:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "笔记API处理器实现完整，支持CRUD操作和TOTP生成"
// }}
// 笔记管理API处理器

use axum::{
    extract::{State, Path, Query, Extension},
    response::Json,
};
use serde::Deserialize;
use tracing::{info, warn, error};
use uuid::Uuid;

use crate::auth::middleware::get_current_user;
use crate::auth::models::Claims;
use crate::database::DatabaseManager;
use super::service::NoteService;
use shared::{
    SecureNote, CreateNoteRequest, UpdateNoteRequest, NoteType, TotpResponse,
    AppError, AppResult, ApiResponse
};

/// 从JWT密钥派生32字节的主密钥
fn derive_master_key_from_jwt_secret(jwt_secret: &str) -> AppResult<Vec<u8>> {
    use ring::digest;

    // 使用SHA-256哈希JWT密钥来生成32字节的主密钥
    let digest = digest::digest(&digest::SHA256, jwt_secret.as_bytes());
    Ok(digest.as_ref().to_vec())
}

/// 查询参数
#[derive(Debug, Deserialize)]
pub struct SearchQuery {
    pub q: Option<String>,
    pub page: Option<u32>,
    pub limit: Option<u32>,
}

#[derive(Debug, Deserialize)]
pub struct TypeQuery {
    pub note_type: String,
}

/// 笔记服务实例
static NOTE_SERVICE: once_cell::sync::Lazy<NoteService> = once_cell::sync::Lazy::new(|| {
    NoteService::new()
});

/// 获取用户所有笔记
pub async fn get_notes_handler(
    State(db_manager): State<DatabaseManager>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<Vec<SecureNote>>>> {
    info!("Fetching notes for user: {}", claims.email);

    // 从配置获取JWT密钥作为临时主密钥（实际应该从用户会话中获取）
    let config = crate::config::Config::load().map_err(|e| AppError::Internal(e.to_string()))?;
    let master_key = derive_master_key_from_jwt_secret(&config.jwt_secret)?;

    let notes = NOTE_SERVICE.get_user_notes(&db_manager, &claims.sub, &master_key).await?;

    Ok(Json(ApiResponse::success(notes)))
}

/// 创建新笔记
pub async fn create_note_handler(
    State(db_manager): State<DatabaseManager>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<CreateNoteRequest>,
) -> AppResult<Json<ApiResponse<SecureNote>>> {
    info!("Creating note '{}' for user: {}", request.title, claims.email);

    // 从配置获取JWT密钥作为临时主密钥
    let config = crate::config::Config::load().map_err(|e| AppError::Internal(e.to_string()))?;
    let master_key = derive_master_key_from_jwt_secret(&config.jwt_secret)?;

    let note = NOTE_SERVICE.create_note(&db_manager, &claims.sub, request, &master_key).await?;

    Ok(Json(ApiResponse::success(note)))
}

/// 根据ID获取笔记
pub async fn get_note_handler(
    State(db_manager): State<DatabaseManager>,
    Path(note_id): Path<String>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<SecureNote>>> {
    info!("Fetching note {} for user: {}", note_id, claims.email);

    // 从配置获取JWT密钥作为临时主密钥
    let config = crate::config::Config::load().map_err(|e| AppError::Internal(e.to_string()))?;
    let master_key = derive_master_key_from_jwt_secret(&config.jwt_secret)?;

    let note = NOTE_SERVICE.get_note_by_id(&db_manager, &note_id, &claims.sub, &master_key).await?;

    Ok(Json(ApiResponse::success(note)))
}

/// 更新笔记
pub async fn update_note_handler(
    State(db_manager): State<DatabaseManager>,
    Path(note_id): Path<String>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<UpdateNoteRequest>,
) -> AppResult<Json<ApiResponse<SecureNote>>> {
    info!("Updating note {} for user: {}", note_id, claims.email);

    // 从配置获取JWT密钥作为临时主密钥
    let config = crate::config::Config::load().map_err(|e| AppError::Internal(e.to_string()))?;
    let master_key = derive_master_key_from_jwt_secret(&config.jwt_secret)?;

    let note = NOTE_SERVICE.update_note(&db_manager, &note_id, &claims.sub, request, &master_key).await?;

    Ok(Json(ApiResponse::success(note)))
}

/// 删除笔记
pub async fn delete_note_handler(
    State(db_manager): State<DatabaseManager>,
    Path(note_id): Path<String>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<()>>> {
    info!("Deleting note {} for user: {}", note_id, claims.email);

    NOTE_SERVICE.delete_note(&db_manager, &note_id, &claims.sub).await?;

    Ok(Json(ApiResponse::success(())))
}

/// 根据类型获取笔记
pub async fn get_notes_by_type_handler(
    State(db_manager): State<DatabaseManager>,
    Path(note_type): Path<String>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<Vec<SecureNote>>>> {
    info!("Fetching {} notes for user: {}", note_type, claims.email);

    // 解析笔记类型
    let parsed_type = match note_type.as_str() {
        "login" => NoteType::Login,
        "ssh_key" => NoteType::SshKey,
        "api_key" => NoteType::ApiKey,
        "certificate" => NoteType::Certificate,
        "database" => NoteType::Database,
        "totp" => NoteType::Totp,
        "tech_note" => NoteType::TechNote,
        _ => return Err(AppError::BadRequest("Invalid note type".to_string())),
    };

    // 从配置获取JWT密钥作为临时主密钥
    let config = crate::config::Config::load().map_err(|e| AppError::Internal(e.to_string()))?;
    let master_key = derive_master_key_from_jwt_secret(&config.jwt_secret)?;

    let notes = NOTE_SERVICE.get_notes_by_type(&db_manager, &claims.sub, &parsed_type, &master_key).await?;

    Ok(Json(ApiResponse::success(notes)))
}

/// 搜索笔记
pub async fn search_notes_handler(
    State(db_manager): State<DatabaseManager>,
    Query(params): Query<SearchQuery>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<Vec<SecureNote>>>> {
    let query = params.q.unwrap_or_default();
    info!("Searching notes for user: {} with query: {}", claims.email, query);

    if query.is_empty() {
        return Err(AppError::BadRequest("Search query is required".to_string()));
    }

    // 从配置获取JWT密钥作为临时主密钥
    let config = crate::config::Config::load().map_err(|e| AppError::Internal(e.to_string()))?;
    let master_key = derive_master_key_from_jwt_secret(&config.jwt_secret)?;

    let notes = NOTE_SERVICE.search_notes(&db_manager, &claims.sub, &query, &master_key).await?;

    Ok(Json(ApiResponse::success(notes)))
}

/// 生成TOTP验证码
pub async fn generate_totp_handler(
    State(db_manager): State<DatabaseManager>,
    Path(note_id): Path<String>,
    Extension(claims): Extension<Claims>,
) -> AppResult<Json<ApiResponse<TotpResponse>>> {
    info!("Generating TOTP for note {} user: {}", note_id, claims.email);

    // 从配置获取JWT密钥作为临时主密钥
    let config = crate::config::Config::load().map_err(|e| AppError::Internal(e.to_string()))?;
    let master_key = derive_master_key_from_jwt_secret(&config.jwt_secret)?;

    let totp_response = NOTE_SERVICE.generate_totp(&db_manager, &note_id, &claims.sub, &master_key).await?;

    Ok(Json(ApiResponse::success(totp_response)))
}
