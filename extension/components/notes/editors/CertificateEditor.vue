<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "证书编辑器组件基础实现"
}} -->
<template>
  <div class="certificate-editor">
    <n-space vertical size="medium">
      <n-form-item label="域名" path="domain">
        <n-input v-model:value="localValue.domain" :disabled="disabled" />
      </n-form-item>
      <n-form-item label="证书" path="certificate">
        <n-input v-model:value="localValue.certificate" type="textarea" :rows="4" :disabled="disabled" />
      </n-form-item>
      <n-form-item label="私钥" path="private_key">
        <n-input v-model:value="localValue.private_key" type="textarea" :rows="4" :disabled="disabled" />
      </n-form-item>
      <n-form-item label="过期时间" path="expires_at">
        <n-input v-model:value="localValue.expires_at" :disabled="disabled" />
      </n-form-item>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { NSpace, NFormItem, NInput } from 'naive-ui';
import type { CertificateContent } from '../../../types';

interface Props {
  value: CertificateContent;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), { disabled: false });
const emit = defineEmits<{ 'update:value': [value: CertificateContent] }>();
const localValue = ref<CertificateContent>({ ...props.value });

watch(localValue, (newValue) => emit('update:value', { ...newValue }), { deep: true });
watch(() => props.value, (newValue) => { localValue.value = { ...newValue }; }, { deep: true });
</script>

<style scoped>
.certificate-editor { width: 100%; }
</style>
