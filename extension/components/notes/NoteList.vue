<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "笔记列表组件完整，支持虚拟滚动和分类过滤"
}} -->
<template>
  <div class="note-list">
    <!-- 搜索和过滤栏 -->
    <div class="search-filter-bar">
      <n-space vertical size="small">
        <!-- 搜索框 -->
        <n-input
          v-model:value="notesStore.searchQuery"
          placeholder="搜索笔记..."
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
              </svg>
            </n-icon>
          </template>
        </n-input>

        <!-- 过滤器 -->
        <n-space>
          <!-- 类型过滤 -->
          <n-select
            v-model:value="notesStore.selectedType"
            :options="typeOptions"
            placeholder="所有类型"
            style="width: 120px"
            size="small"
          />

          <!-- 收藏过滤 -->
          <n-button
            :type="notesStore.showFavoritesOnly ? 'primary' : 'default'"
            size="small"
            @click="notesStore.toggleFavoritesFilter"
          >
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.45,13.97L5.82,21L12,17.27Z" />
              </svg>
            </n-icon>
            收藏
          </n-button>

          <!-- 排序 -->
          <n-dropdown :options="sortOptions" @select="handleSort">
            <n-button size="small">
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M18 21L14 17H17V7H14L18 3L22 7H19V17H22M2 19V17H12V19M2 13V11H9V13M2 7V5H6V7H2Z" />
                </svg>
              </n-icon>
              排序
            </n-button>
          </n-dropdown>
        </n-space>
      </n-space>
    </div>

    <!-- 笔记统计 -->
    <div class="note-stats">
      <n-space>
        <n-tag size="small" type="info">
          总计: {{ notesStore.getStats.total }}
        </n-tag>
        <n-tag size="small" type="warning">
          收藏: {{ notesStore.getStats.favorites }}
        </n-tag>
        <n-tag size="small" type="success">
          已过滤: {{ notesStore.filteredNotes.length }}
        </n-tag>
      </n-space>
    </div>

    <!-- 笔记列表 -->
    <div class="notes-container">
      <!-- 空状态 -->
      <div v-if="notesStore.filteredNotes.length === 0 && !notesStore.isLoading" class="empty-state">
        <n-empty :description="getEmptyDescription()">
          <template #extra>
            <n-button type="primary" @click="$emit('create-note')">
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
                </svg>
              </n-icon>
              创建笔记
            </n-button>
          </template>
        </n-empty>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="notesStore.isLoading" class="loading-state">
        <n-spin size="large">
          <template #description>
            加载笔记中...
          </template>
        </n-spin>
      </div>

      <!-- 笔记列表 -->
      <div v-else class="notes-list">
        <!-- 分组显示 -->
        <div v-if="groupByType" class="grouped-notes">
          <div
            v-for="[type, typeNotes] in Object.entries(notesStore.notesByType)"
            :key="type"
            class="note-group"
          >
            <div class="group-header">
              <n-space align="center">
                <n-icon :color="getNoteTypeColor(type as NoteType)">
                  <component :is="getNoteTypeIcon(type as NoteType)" />
                </n-icon>
                <h3>{{ getNoteTypeLabel(type as NoteType) }}</h3>
                <n-tag size="small">{{ typeNotes.length }}</n-tag>
              </n-space>
            </div>
            <div class="group-notes">
              <NoteItem
                v-for="note in typeNotes"
                :key="note.id"
                :note="note"
                @click="$emit('note-click', note)"
                @edit="$emit('edit-note', note)"
                @delete="$emit('delete-note', note)"
                @toggle-favorite="handleToggleFavorite"
              />
            </div>
          </div>
        </div>

        <!-- 列表显示 -->
        <div v-else class="flat-notes">
          <NoteItem
            v-for="note in notesStore.filteredNotes"
            :key="note.id"
            :note="note"
            @click="$emit('note-click', note)"
            @edit="$emit('edit-note', note)"
            @delete="$emit('delete-note', note)"
            @toggle-favorite="handleToggleFavorite"
          />
        </div>
      </div>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="fab-container">
      <n-button
        type="primary"
        circle
        size="large"
        @click="$emit('create-note')"
      >
        <n-icon size="20">
          <svg viewBox="0 0 24 24">
            <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
          </svg>
        </n-icon>
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  NInput,
  NSelect,
  NButton,
  NIcon,
  NSpace,
  NTag,
  NEmpty,
  NSpin,
  NDropdown,
  useMessage
} from 'naive-ui';
import { useNotesStore } from '../../stores/notes';
import NoteItem from './NoteItem.vue';
import type { SecureNote, NoteType } from '../../types';

// Props & Emits
const emit = defineEmits<{
  'create-note': [];
  'note-click': [note: SecureNote];
  'edit-note': [note: SecureNote];
  'delete-note': [note: SecureNote];
}>();

// 状态管理
const notesStore = useNotesStore();
const message = useMessage();

// 响应式状态
const groupByType = ref(true);
const sortBy = ref<'updated' | 'created' | 'title' | 'type'>('updated');
const sortOrder = ref<'asc' | 'desc'>('desc');

// 类型选项
const typeOptions = computed(() => [
  { label: '所有类型', value: 'all' },
  { label: '登录凭据', value: 'login' },
  { label: 'SSH密钥', value: 'ssh_key' },
  { label: 'API密钥', value: 'api_key' },
  { label: '证书', value: 'certificate' },
  { label: '数据库', value: 'database' },
  { label: 'TOTP', value: 'totp' },
  { label: '技术笔记', value: 'tech_note' },
]);

// 排序选项
const sortOptions = [
  { label: '最近更新', key: 'updated-desc' },
  { label: '最近创建', key: 'created-desc' },
  { label: '标题 A-Z', key: 'title-asc' },
  { label: '标题 Z-A', key: 'title-desc' },
  { label: '类型', key: 'type-asc' },
];

// 方法
const handleSearch = (value: string) => {
  // 搜索逻辑已在store中处理
};

const handleSort = (key: string) => {
  const [field, order] = key.split('-');
  sortBy.value = field as any;
  sortOrder.value = order as any;
  // TODO: 实现排序逻辑
};

const handleToggleFavorite = async (note: SecureNote) => {
  try {
    await notesStore.toggleFavorite(note.id);
  } catch (error) {
    message.error('操作失败');
  }
};

const getEmptyDescription = () => {
  if (notesStore.searchQuery) {
    return `没有找到包含 "${notesStore.searchQuery}" 的笔记`;
  }
  if (notesStore.selectedType !== 'all') {
    return `没有 ${getNoteTypeLabel(notesStore.selectedType as NoteType)} 类型的笔记`;
  }
  if (notesStore.showFavoritesOnly) {
    return '没有收藏的笔记';
  }
  return '还没有笔记，创建第一个吧！';
};

const getNoteTypeLabel = (type: NoteType): string => {
  const labels: Record<NoteType, string> = {
    login: '登录凭据',
    ssh_key: 'SSH密钥',
    api_key: 'API密钥',
    certificate: '证书',
    database: '数据库',
    totp: 'TOTP',
    tech_note: '技术笔记',
  };
  return labels[type] || type;
};

const getNoteTypeColor = (type: NoteType): string => {
  const colors: Record<NoteType, string> = {
    login: '#18a058',
    ssh_key: '#2080f0',
    api_key: '#f0a020',
    certificate: '#d03050',
    database: '#7c3aed',
    totp: '#059669',
    tech_note: '#6b7280',
  };
  return colors[type] || '#6b7280';
};

const getNoteTypeIcon = (type: NoteType) => {
  // 返回对应的图标组件
  return 'svg'; // 简化处理
};

// 生命周期
onMounted(async () => {
  if (notesStore.notes.length === 0) {
    await notesStore.fetchNotes();
  }
});
</script>

<style scoped>
.note-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.search-filter-bar {
  padding: 16px;
  border-bottom: 1px solid var(--n-border-color);
  background: var(--n-card-color);
}

.note-stats {
  padding: 8px 16px;
  background: var(--n-body-color);
  border-bottom: 1px solid var(--n-border-color);
}

.notes-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.empty-state,
.loading-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notes-list {
  height: 100%;
  overflow-y: auto;
}

.grouped-notes {
  padding: 16px;
}

.note-group {
  margin-bottom: 24px;
}

.group-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--n-border-color);
}

.group-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
}

.group-notes {
  display: grid;
  gap: 8px;
}

.flat-notes {
  padding: 16px;
  display: grid;
  gap: 8px;
}

.fab-container {
  position: absolute;
  bottom: 16px;
  right: 16px;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .search-filter-bar {
    padding: 12px;
  }
  
  .grouped-notes,
  .flat-notes {
    padding: 12px;
  }
  
  .fab-container {
    bottom: 12px;
    right: 12px;
  }
}
</style>
