# Rust
/target/
**/*.rs.bk
Cargo.lock

# Database
*.db
*.sqlite
*.sqlite3

# Logs
*.log

# Environment variables
.env
.env.local
.env.*.local

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Extension build
.wxt/
.output/
dist/
*.zip

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
