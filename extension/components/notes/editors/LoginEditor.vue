<!-- {{RIPER-5:
  Action: "Added"
  Task_ID: "#f100df02-1248-4a2e-9ce3-628ca14bf113"
  Timestamp: "2025-08-05T17:30:00Z"
  Authoring_Role: "LD"
  Principle_Applied: "SOLID-S (单一职责原则)"
  Quality_Check: "登录编辑器组件完整，支持URL验证和密码生成"
}} -->
<template>
  <div class="login-editor">
    <n-space vertical size="medium">
      <!-- URL -->
      <n-form-item label="网站地址" path="url">
        <n-input
          v-model:value="localValue.url"
          placeholder="https://example.com"
          :disabled="disabled"
          @blur="validateUrl"
        >
          <template #prefix>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M16.36,14C16.44,13.34 16.5,12.68 16.5,12C16.5,11.32 16.44,10.66 16.36,10H19.74C19.9,10.64 20,11.31 20,12C20,12.69 19.9,13.36 19.74,14M14.59,19.56C15.19,18.45 15.65,17.25 15.97,16H18.92C17.96,17.65 16.43,18.93 14.59,19.56M14.34,14H9.66C9.56,13.34 9.5,12.68 9.5,12C9.5,11.32 9.56,10.65 9.66,10H14.34C14.43,10.65 14.5,11.32 14.5,12C14.5,12.68 14.43,13.34 14.34,14M12,19.96C11.17,18.76 10.5,17.43 10.09,16H13.91C13.5,17.43 12.83,18.76 12,19.96M8,8H5.08C6.03,6.34 7.57,5.06 9.4,4.44C8.8,5.55 8.35,6.75 8,8M5.08,16H8C8.35,17.25 8.8,18.45 9.4,19.56C7.57,18.93 6.03,17.65 5.08,16M4.26,14C4.1,13.36 4,12.69 4,12C4,11.31 4.1,10.64 4.26,10H7.64C7.56,10.66 7.5,11.32 7.5,12C7.5,12.68 7.56,13.34 7.64,14M12,4.03C12.83,5.23 13.5,6.57 13.91,8H10.09C10.5,6.57 11.17,5.23 12,4.03M18.92,8H15.97C15.65,6.75 15.19,5.55 14.59,4.44C16.43,5.07 17.96,6.34 18.92,8M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
              </svg>
            </n-icon>
          </template>
          <template #suffix>
            <n-button
              v-if="isValidUrl"
              text
              size="small"
              @click="openUrl"
            >
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z" />
                </svg>
              </n-icon>
            </n-button>
          </template>
        </n-input>
      </n-form-item>

      <!-- 用户名/邮箱 -->
      <n-form-item label="用户名/邮箱" path="username">
        <n-input
          v-model:value="localValue.username"
          placeholder="请输入用户名或邮箱"
          :disabled="disabled"
        >
          <template #prefix>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" />
              </svg>
            </n-icon>
          </template>
        </n-input>
      </n-form-item>

      <!-- 密码 -->
      <n-form-item label="密码" path="password">
        <n-input
          v-model:value="localValue.password"
          :type="showPassword ? 'text' : 'password'"
          placeholder="请输入密码"
          :disabled="disabled"
        >
          <template #prefix>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
              </svg>
            </n-icon>
          </template>
          <template #suffix>
            <n-space>
              <!-- 显示/隐藏密码 -->
              <n-button
                text
                size="small"
                @click="showPassword = !showPassword"
              >
                <n-icon>
                  <svg v-if="showPassword" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z" />
                  </svg>
                  <svg v-else viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z" />
                  </svg>
                </n-icon>
              </n-button>
              
              <!-- 生成密码 -->
              <n-button
                text
                size="small"
                @click="generatePassword"
                :disabled="disabled"
              >
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
                  </svg>
                </n-icon>
              </n-button>
              
              <!-- 复制密码 -->
              <n-button
                text
                size="small"
                @click="copyPassword"
                :disabled="!localValue.password"
              >
                <n-icon>
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" />
                  </svg>
                </n-icon>
              </n-button>
            </n-space>
          </template>
        </n-input>
        
        <!-- 密码强度指示器 -->
        <div v-if="localValue.password" class="password-strength">
          <div class="strength-bar">
            <div 
              class="strength-fill" 
              :class="passwordStrength.level"
              :style="{ width: passwordStrength.percentage + '%' }"
            ></div>
          </div>
          <span class="strength-text" :class="passwordStrength.level">
            {{ passwordStrength.text }}
          </span>
        </div>
      </n-form-item>

      <!-- TOTP密钥 (可选) -->
      <n-form-item label="TOTP密钥 (可选)" path="totp_secret">
        <n-input
          v-model:value="localValue.totp_secret"
          placeholder="用于两步验证的密钥"
          :disabled="disabled"
        >
          <template #prefix>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.11,7 14,7.89 14,9C14,10.11 13.11,11 12,11C10.89,11 10,10.11 10,9C10,7.89 10.89,7 12,7M12,14.3C13.07,14.3 14.8,14.5 15.6,15.6C14.5,16.9 13.3,17.5 12,17.5C10.7,17.5 9.5,16.9 8.4,15.6C9.2,14.5 10.93,14.3 12,14.3Z" />
              </svg>
            </n-icon>
          </template>
        </n-input>
      </n-form-item>

      <!-- 备注 -->
      <n-form-item label="备注" path="notes">
        <n-input
          v-model:value="localValue.notes"
          type="textarea"
          placeholder="添加备注信息..."
          :rows="3"
          :disabled="disabled"
        />
      </n-form-item>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  NSpace,
  NFormItem,
  NInput,
  NButton,
  NIcon,
  useMessage
} from 'naive-ui';
import { usePasswordStrength } from '../../../composables/useAuth';
import type { LoginContent } from '../../../types';

// Props & Emits
interface Props {
  value: LoginContent;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});

const emit = defineEmits<{
  'update:value': [value: LoginContent];
}>();

// 状态
const showPassword = ref(false);
const message = useMessage();
const { evaluateStrength, generateStrongPassword } = usePasswordStrength();

// 本地值
const localValue = ref<LoginContent>({ ...props.value });

// 计算属性
const isValidUrl = computed(() => {
  try {
    new URL(localValue.value.url);
    return true;
  } catch {
    return false;
  }
});

const passwordStrength = computed(() => {
  return evaluateStrength(localValue.value.password || '');
});

// 方法
const validateUrl = () => {
  if (localValue.value.url && !isValidUrl.value) {
    message.warning('请输入有效的URL地址');
  }
};

const openUrl = () => {
  if (isValidUrl.value) {
    chrome.tabs.create({ url: localValue.value.url });
  }
};

const generatePassword = () => {
  const newPassword = generateStrongPassword(16);
  localValue.value.password = newPassword;
  message.success('密码已生成');
};

const copyPassword = async () => {
  try {
    await navigator.clipboard.writeText(localValue.value.password);
    message.success('密码已复制到剪贴板');
  } catch (error) {
    message.error('复制失败');
  }
};

// 监听变化
watch(localValue, (newValue) => {
  emit('update:value', { ...newValue });
}, { deep: true });

watch(() => props.value, (newValue) => {
  localValue.value = { ...newValue };
}, { deep: true });
</script>

<style scoped>
.login-editor {
  width: 100%;
}

.password-strength {
  margin-top: 8px;
}

.strength-bar {
  height: 4px;
  background-color: var(--n-border-color);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.strength-fill {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
}

.strength-fill.weak { background-color: #e74c3c; }
.strength-fill.medium { background-color: #f39c12; }
.strength-fill.strong { background-color: #3498db; }
.strength-fill.very-strong { background-color: #27ae60; }

.strength-text {
  font-size: 12px;
}

.strength-text.weak { color: #e74c3c; }
.strength-text.medium { color: #f39c12; }
.strength-text.strong { color: #3498db; }
.strength-text.very-strong { color: #27ae60; }

:deep(.n-input-wrapper) {
  --n-height: 36px;
}
</style>
