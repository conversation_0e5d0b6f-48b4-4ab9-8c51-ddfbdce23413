// {{RIPER-5:
//   Action: "Modified"
//   Task_ID: "#a194fea0-d065-4b72-85be-8e349bb0f85e"
//   Timestamp: "2025-08-05T16:40:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "Content script基础框架完整，支持表单检测和消息通信"
// }}
// Content Script - 页面内容脚本

import { messageRouter, MessageType } from '../../utils/messaging';

console.log('SecureFox content script loaded on:', window.location.href);

// 初始化
function initialize() {
  try {
    // 设置消息处理器
    setupMessageHandlers();

    // 启动消息监听
    messageRouter.startListening();

    // 检测页面表单
    detectForms();

    console.log('Content script initialized successfully');
  } catch (error) {
    console.error('Failed to initialize content script:', error);
  }
}

// 设置消息处理器
function setupMessageHandlers() {
  // Ping响应
  messageRouter.register(MessageType.PING, async () => {
    return { success: true, data: 'Content script is active' };
  });

  // 检测表单
  messageRouter.register(MessageType.DETECT_FORMS, async () => {
    try {
      const forms = detectForms();
      return { success: true, data: forms };
    } catch (error) {
      return { success: false, error: 'Failed to detect forms' };
    }
  });

  // 填充表单
  messageRouter.register(MessageType.FILL_FORM, async (payload) => {
    try {
      const { username, password } = payload;
      const filled = await fillLoginForm(username, password);
      return { success: filled, data: filled ? 'Form filled successfully' : 'No suitable form found' };
    } catch (error) {
      return { success: false, error: 'Failed to fill form' };
    }
  });

  // 捕获凭据
  messageRouter.register(MessageType.CAPTURE_CREDENTIALS, async () => {
    try {
      const credentials = captureCredentials();
      return { success: true, data: credentials };
    } catch (error) {
      return { success: false, error: 'Failed to capture credentials' };
    }
  });

  // 页面加载完成
  messageRouter.register('PAGE_LOADED' as MessageType, async (payload) => {
    try {
      console.log('Page loaded:', payload.url);
      // 重新检测表单
      detectForms();
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to handle page load' };
    }
  });
}

// 检测页面中的登录表单
function detectForms() {
  const forms: any[] = [];

  try {
    // 查找所有表单
    const formElements = document.querySelectorAll('form');

    formElements.forEach((form, index) => {
      const usernameField = findUsernameField(form);
      const passwordField = findPasswordField(form);

      if (usernameField && passwordField) {
        const formInfo = {
          index,
          url: window.location.href,
          title: document.title,
          action: form.action || window.location.href,
          method: form.method || 'POST',
          fields: {
            username: {
              selector: getElementSelector(usernameField),
              type: usernameField.type,
              name: usernameField.name,
              id: usernameField.id,
            },
            password: {
              selector: getElementSelector(passwordField),
              type: passwordField.type,
              name: passwordField.name,
              id: passwordField.id,
            },
          },
        };

        forms.push(formInfo);
        console.log('Detected login form:', formInfo);
      }
    });

    // 如果没有找到表单中的字段，尝试查找页面中的独立字段
    if (forms.length === 0) {
      const usernameField = findUsernameField(document);
      const passwordField = findPasswordField(document);

      if (usernameField && passwordField) {
        const formInfo = {
          index: -1, // 表示不在表单中
          url: window.location.href,
          title: document.title,
          action: window.location.href,
          method: 'POST',
          fields: {
            username: {
              selector: getElementSelector(usernameField),
              type: usernameField.type,
              name: usernameField.name,
              id: usernameField.id,
            },
            password: {
              selector: getElementSelector(passwordField),
              type: passwordField.type,
              name: passwordField.name,
              id: passwordField.id,
            },
          },
        };

        forms.push(formInfo);
        console.log('Detected standalone login fields:', formInfo);
      }
    }
  } catch (error) {
    console.error('Error detecting forms:', error);
  }

  return forms;
}

// 查找用户名字段
function findUsernameField(container: Element | Document): HTMLInputElement | null {
  const selectors = [
    'input[type="email"]',
    'input[type="text"][name*="user"]',
    'input[type="text"][name*="email"]',
    'input[type="text"][name*="login"]',
    'input[type="text"][id*="user"]',
    'input[type="text"][id*="email"]',
    'input[type="text"][id*="login"]',
    'input[type="text"][placeholder*="email"]',
    'input[type="text"][placeholder*="username"]',
    'input[type="text"][placeholder*="user"]',
  ];

  for (const selector of selectors) {
    const field = container.querySelector(selector) as HTMLInputElement;
    if (field && isVisible(field)) {
      return field;
    }
  }

  return null;
}

// 查找密码字段
function findPasswordField(container: Element | Document): HTMLInputElement | null {
  const passwordFields = container.querySelectorAll('input[type="password"]') as NodeListOf<HTMLInputElement>;

  for (const field of passwordFields) {
    if (isVisible(field)) {
      return field;
    }
  }

  return null;
}

// 检查元素是否可见
function isVisible(element: HTMLElement): boolean {
  const style = window.getComputedStyle(element);
  return style.display !== 'none' &&
         style.visibility !== 'hidden' &&
         style.opacity !== '0' &&
         element.offsetWidth > 0 &&
         element.offsetHeight > 0;
}

// 获取元素选择器
function getElementSelector(element: HTMLElement): string {
  if (element.id) {
    return `#${element.id}`;
  }

  if (element.name) {
    return `[name="${element.name}"]`;
  }

  // 生成基于类名和标签的选择器
  let selector = element.tagName.toLowerCase();

  if (element.className) {
    const classes = element.className.split(' ').filter(c => c.trim());
    if (classes.length > 0) {
      selector += '.' + classes.join('.');
    }
  }

  return selector;
}

// 填充登录表单
async function fillLoginForm(username: string, password: string): Promise<boolean> {
  try {
    const forms = detectForms();

    if (forms.length === 0) {
      console.log('No login forms found');
      return false;
    }

    const form = forms[0]; // 使用第一个找到的表单

    // 填充用户名
    const usernameField = document.querySelector(form.fields.username.selector) as HTMLInputElement;
    if (usernameField) {
      usernameField.value = username;
      usernameField.dispatchEvent(new Event('input', { bubbles: true }));
      usernameField.dispatchEvent(new Event('change', { bubbles: true }));
    }

    // 填充密码
    const passwordField = document.querySelector(form.fields.password.selector) as HTMLInputElement;
    if (passwordField) {
      passwordField.value = password;
      passwordField.dispatchEvent(new Event('input', { bubbles: true }));
      passwordField.dispatchEvent(new Event('change', { bubbles: true }));
    }

    console.log('Form filled successfully');
    return true;
  } catch (error) {
    console.error('Error filling form:', error);
    return false;
  }
}

// 捕获页面中的凭据
function captureCredentials() {
  const forms = detectForms();
  const credentials: any[] = [];

  forms.forEach(form => {
    const usernameField = document.querySelector(form.fields.username.selector) as HTMLInputElement;
    const passwordField = document.querySelector(form.fields.password.selector) as HTMLInputElement;

    if (usernameField?.value && passwordField?.value) {
      credentials.push({
        url: window.location.href,
        title: document.title,
        username: usernameField.value,
        password: passwordField.value,
      });
    }
  });

  return credentials;
}

// 监听表单提交
document.addEventListener('submit', (event) => {
  const form = event.target as HTMLFormElement;

  try {
    const usernameField = findUsernameField(form);
    const passwordField = findPasswordField(form);

    if (usernameField?.value && passwordField?.value) {
      // 发送凭据到background script
      chrome.runtime.sendMessage({
        type: 'CREDENTIALS_DETECTED',
        data: {
          url: window.location.href,
          title: document.title,
          username: usernameField.value,
          // 注意：实际实现中不应该发送明文密码
          hasPassword: true,
        },
      }).catch(console.error);
    }
  } catch (error) {
    console.error('Error handling form submit:', error);
  }
});

// 启动初始化
initialize();

export {};
