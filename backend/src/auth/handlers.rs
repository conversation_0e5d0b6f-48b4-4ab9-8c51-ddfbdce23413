// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#8db4a073-e2f1-4c70-9e28-e31d3e70f268"
//   Timestamp: "2025-08-05T15:30:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "认证API处理器实现完整，支持注册、登录、验证、登出"
// }}
// 认证API处理器实现

use axum::{
    extract::State,
    response::Json,
};
use base64::{Engine as _, engine::general_purpose};
use jsonwebtoken::{encode, decode, Header, Validation, EncodingKey, DecodingKey};
use tracing::{info, warn, error};

use crate::database::{DatabaseManager, CreateUserRequest};
use crate::database::repository::UserRepository;
use crate::crypto::{CryptoService, DefaultCryptoService};
use super::models::{Claims, PasswordStrength, AuthError};
use shared::{RegisterRequest, LoginRequest, AuthResponse, AppError, AppResult};

/// 认证服务
pub struct AuthService {
    crypto_service: DefaultCryptoService,
    jwt_secret: String,
}

impl AuthService {
    pub fn new(jwt_secret: String) -> Self {
        Self {
            crypto_service: DefaultCryptoService::new(),
            jwt_secret,
        }
    }

    /// 生成JWT token
    pub fn generate_token(&self, user_id: &str, email: &str) -> Result<String, AuthError> {
        let claims = Claims::new(user_id, email, 24); // 24小时过期
        let header = Header::default();
        let encoding_key = EncodingKey::from_secret(self.jwt_secret.as_ref());

        let token = encode(&header, &claims, &encoding_key)?;
        Ok(token)
    }

    /// 验证JWT token
    pub fn verify_token(&self, token: &str) -> Result<Claims, AuthError> {
        let decoding_key = DecodingKey::from_secret(self.jwt_secret.as_ref());
        let validation = Validation::default();

        let token_data = decode::<Claims>(token, &decoding_key, &validation)?;
        let claims = token_data.claims;

        if claims.is_expired() {
            return Err(AuthError::TokenExpired);
        }

        Ok(claims)
    }

    /// 验证并派生密钥
    async fn derive_and_store_key(&self, password: &str, salt: &[u8]) -> Result<Vec<u8>, AuthError> {
        let master_key = self.crypto_service.derive_master_key(password, salt).await?;
        Ok(master_key.as_bytes().to_vec())
    }
}

/// 用户注册处理器
pub async fn register_handler(
    State(db_manager): State<DatabaseManager>,
    Json(request): Json<RegisterRequest>,
) -> AppResult<Json<AuthResponse>> {
    // 从配置中获取JWT密钥
    let config = crate::config::Config::load().map_err(|e| AppError::Internal(e.to_string()))?;
    let auth_service = AuthService::new(config.jwt_secret);
    info!("Processing user registration for email: {}", request.email);

    // 验证密码强度
    let password_strength = PasswordStrength::validate(&request.master_password);
    if !password_strength.is_valid {
        let error_msg = format!("密码强度不足: {}", password_strength.issues.join(", "));
        warn!("Password validation failed for {}: {}", request.email, error_msg);
        return Err(AuthError::WeakPassword(error_msg).into());
    }

    // 检查用户是否已存在
    let user_repo = db_manager.user_repository();
    if let Ok(Some(_)) = user_repo.find_user_by_email(&request.email).await {
        warn!("Registration attempt for existing email: {}", request.email);
        return Err(AuthError::UserExists.into());
    }

    // 生成盐值
    let salt = auth_service.crypto_service.generate_salt().await
        .map_err(AuthError::Crypto)?;

    // 派生并存储密钥哈希
    let password_hash = auth_service.derive_and_store_key(&request.master_password, &salt).await?;

    // 创建用户
    let create_request = CreateUserRequest {
        email: request.email.clone(),
        password_hash: general_purpose::STANDARD.encode(&password_hash),
        salt: general_purpose::STANDARD.encode(&salt),
    };

    let db_user = user_repo.create_user(create_request).await?;
    let user = db_user.to_user();

    // 生成JWT token
    let token = auth_service.generate_token(&db_user.id, &db_user.email)
        .map_err(|e| AppError::Internal(e.to_string()))?;

    info!("User registration successful for: {}", request.email);

    Ok(Json(AuthResponse { user, token }))
}

/// 用户登录处理器
pub async fn login_handler(
    State(db_manager): State<DatabaseManager>,
    Json(request): Json<LoginRequest>,
) -> AppResult<Json<AuthResponse>> {
    // 从配置中获取JWT密钥
    let config = crate::config::Config::load().map_err(|e| AppError::Internal(e.to_string()))?;
    let auth_service = AuthService::new(config.jwt_secret);
    info!("Processing user login for email: {}", request.email);

    // 查找用户
    let user_repo = db_manager.user_repository();
    let db_user = user_repo.find_user_by_email(&request.email).await?
        .ok_or_else(|| {
            warn!("Login attempt for non-existent email: {}", request.email);
            AuthError::InvalidCredentials
        })?;

    // 解码存储的盐值和密码哈希
    let salt = general_purpose::STANDARD.decode(&db_user.salt)
        .map_err(|e| {
            error!("Failed to decode salt for user {}: {}", request.email, e);
            AuthError::InvalidCredentials
        })?;

    let stored_hash = general_purpose::STANDARD.decode(&db_user.password_hash)
        .map_err(|e| {
            error!("Failed to decode password hash for user {}: {}", request.email, e);
            AuthError::InvalidCredentials
        })?;

    // 验证主密码
    let is_valid = auth_service.crypto_service
        .verify_master_password(&request.master_password, &salt, &stored_hash)
        .await
        .map_err(AuthError::Crypto)?;

    if !is_valid {
        warn!("Invalid password attempt for email: {}", request.email);
        return Err(AuthError::InvalidCredentials.into());
    }

    let user = db_user.to_user();

    // 生成JWT token
    let token = auth_service.generate_token(&db_user.id, &db_user.email)
        .map_err(|e| AppError::Internal(e.to_string()))?;

    info!("User login successful for: {}", request.email);

    Ok(Json(AuthResponse { user, token }))
}

/// 会话验证处理器
pub async fn verify_handler() -> AppResult<Json<serde_json::Value>> {
    // 这个处理器将在中间件验证JWT后被调用
    // 如果能到达这里，说明JWT验证已经通过
    info!("Session verification successful");

    Ok(Json(serde_json::json!({
        "valid": true,
        "message": "Token is valid"
    })))
}

/// 用户登出处理器
pub async fn logout_handler() -> AppResult<Json<serde_json::Value>> {
    // 在无状态JWT系统中，登出主要在客户端处理（删除token）
    // 这里返回成功响应
    info!("User logout processed");

    Ok(Json(serde_json::json!({
        "message": "Logout successful"
    })))
}
